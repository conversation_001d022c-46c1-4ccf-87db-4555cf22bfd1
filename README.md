# JNX Project Manager

A Vue.js project management application built with TypeScript, Tailwind CSS, and Pinia for state management.

## Features

- **Authentication System**: Complete login/logout functionality with JWT tokens
- **Customer Management**: CRUD operations for customer data
- **Equipment Tracking**: Monitor and manage equipment
- **Ticket System**: Handle support tickets and issues
- **Dashboard**: Overview of key metrics and data

## Authentication

The application includes a complete authentication system with:

- Login page with form validation
- JWT token-based authentication
- Route guards to protect authenticated pages
- Automatic token refresh and validation
- Logout functionality

### Test Accounts (Development)

When running in development mode with the local API server:

- **Admin User**
  - Username: `admin`
  - Password: `admin123`

- **Regular User**
  - Username: `john.doe`
  - Password: `password123`

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
yarn
```

### Development with Local API Server

For development and testing, you can run a local API server:

1. Install server dependencies:
```sh
cd server
npm install
```

2. Start the API server:
```sh
cd server
npm start
```

3. In a new terminal, start the frontend development server:
```sh
yarn dev
```

The application will automatically use the local API server in development mode.

### Compile and Hot-Reload for Development

```sh
yarn dev
```

### Type-Check, Compile and Minify for Production

```sh
yarn build
```
