<template>
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-white px-4 py-3 border-t border-gray-200">
    <!-- Results info -->
    <div class="flex items-center text-sm text-gray-700">
      <span>
        Showing {{ startItem }} to {{ endItem }} of {{ total }} results
      </span>
    </div>

    <!-- Pagination controls -->
    <div class="flex items-center space-x-2">
      <!-- Page size selector -->
      <div class="flex items-center space-x-2">
        <label for="page-size" class="text-sm text-gray-700">Show:</label>
        <select
          id="page-size"
          :value="pageSize"
          @change="handlePageSizeChange"
          class="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="10">10</option>
          <option value="25">25</option>
          <option value="50">50</option>
        </select>
      </div>

      <!-- Page navigation -->
      <nav class="flex items-center space-x-1" aria-label="Pagination">
        <!-- Previous button -->
        <button
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage <= 1"
          class="relative inline-flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors"
          :class="currentPage <= 1 
            ? 'text-gray-300 cursor-not-allowed' 
            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          <span class="sr-only">Previous</span>
        </button>

        <!-- Page numbers -->
        <div class="flex items-center space-x-1">
          <!-- First page -->
          <button
            v-if="showFirstPage"
            @click="goToPage(1)"
            class="relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
            :class="currentPage === 1 
              ? 'bg-blue-600 text-white' 
              : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'"
          >
            1
          </button>

          <!-- First ellipsis -->
          <span v-if="showFirstEllipsis" class="px-2 py-2 text-sm text-gray-500">...</span>

          <!-- Visible page numbers -->
          <button
            v-for="page in visiblePages"
            :key="page"
            @click="goToPage(page)"
            class="relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
            :class="currentPage === page 
              ? 'bg-blue-600 text-white' 
              : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'"
          >
            {{ page }}
          </button>

          <!-- Last ellipsis -->
          <span v-if="showLastEllipsis" class="px-2 py-2 text-sm text-gray-500">...</span>

          <!-- Last page -->
          <button
            v-if="showLastPage"
            @click="goToPage(totalPages)"
            class="relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
            :class="currentPage === totalPages 
              ? 'bg-blue-600 text-white' 
              : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'"
          >
            {{ totalPages }}
          </button>
        </div>

        <!-- Next button -->
        <button
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage >= totalPages"
          class="relative inline-flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors"
          :class="currentPage >= totalPages 
            ? 'text-gray-300 cursor-not-allowed' 
            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
          <span class="sr-only">Next</span>
        </button>
      </nav>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  currentPage: number
  pageSize: number
  total: number
  maxVisiblePages?: number
}

interface Emits {
  (e: 'page-change', page: number): void
  (e: 'page-size-change', pageSize: number): void
}

const props = withDefaults(defineProps<Props>(), {
  maxVisiblePages: 5
})

const emit = defineEmits<Emits>()

// Computed properties
const totalPages = computed(() => Math.ceil(props.total / props.pageSize))

const startItem = computed(() => {
  if (props.total === 0) return 0
  return (props.currentPage - 1) * props.pageSize + 1
})

const endItem = computed(() => {
  const end = props.currentPage * props.pageSize
  return Math.min(end, props.total)
})

// Calculate visible page numbers
const visiblePages = computed(() => {
  const pages: number[] = []
  const maxVisible = props.maxVisiblePages
  const current = props.currentPage
  const total = totalPages.value

  if (total <= maxVisible) {
    // Show all pages if total is less than max visible
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // Calculate start and end of visible range
    let start = Math.max(1, current - Math.floor(maxVisible / 2))
    let end = Math.min(total, start + maxVisible - 1)

    // Adjust start if we're near the end
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1)
    }

    // Don't include first and last page in visible range (they're handled separately)
    if (start === 1) start = 2
    if (end === total) end = total - 1

    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
  }

  return pages
})

const showFirstPage = computed(() => {
  return totalPages.value > 1 && !visiblePages.value.includes(1)
})

const showLastPage = computed(() => {
  return totalPages.value > 1 && !visiblePages.value.includes(totalPages.value)
})

const showFirstEllipsis = computed(() => {
  return showFirstPage.value && visiblePages.value.length > 0 && visiblePages.value[0] > 2
})

const showLastEllipsis = computed(() => {
  return showLastPage.value && visiblePages.value.length > 0 && 
         visiblePages.value[visiblePages.value.length - 1] < totalPages.value - 1
})

// Methods
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value && page !== props.currentPage) {
    emit('page-change', page)
  }
}

const handlePageSizeChange = (event: Event) => {
  const target = event.target as HTMLSelectElement
  const newPageSize = parseInt(target.value)
  emit('page-size-change', newPageSize)
}
</script>
