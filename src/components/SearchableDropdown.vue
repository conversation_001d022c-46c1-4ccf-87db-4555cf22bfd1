<template>
  <div class="relative" ref="dropdownRef">
    <!-- Input Field -->
    <div class="relative">
      <input
        :id="id"
        v-model="searchQuery"
        type="text"
        :placeholder="placeholder"
        :required="required"
        :disabled="disabled"
        @focus="handleFocus"
        @input="handleInput"
        @keydown.down.prevent="navigateDown"
        @keydown.up.prevent="navigateUp"
        @keydown.enter.prevent="selectHighlighted"
        @keydown.escape="closeDropdown"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
        :class="inputClass"
        autocomplete="off"
      />
      <!-- Clear button -->
      <button
        v-if="searchQuery && !disabled"
        type="button"
        @click="clearSelection"
        class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Loading indicator -->
    <div
      v-if="isOpen && loading"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg"
    >
      <div class="px-3 py-2 text-sm text-gray-500 text-center flex items-center justify-center">
        <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading...
      </div>
    </div>

    <!-- Dropdown List -->
    <div
      v-if="isOpen && !loading && filteredOptions.length > 0"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto"
    >
      <ul class="py-1">
        <li
          v-for="(option, index) in filteredOptions"
          :key="option[valueKey]"
          @click="selectOption(option)"
          @mouseenter="highlightedIndex = index"
          class="px-3 py-2 cursor-pointer transition-colors"
          :class="{
            'bg-blue-50 text-blue-700': highlightedIndex === index,
            'hover:bg-gray-50': highlightedIndex !== index
          }"
        >
          <div class="flex items-center">
            <span class="text-sm">{{ option[labelKey] }}</span>
          </div>
        </li>
      </ul>
    </div>

    <!-- No results message -->
    <div
      v-if="isOpen && !loading && searchQuery && filteredOptions.length === 0"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg"
    >
      <div class="px-3 py-2 text-sm text-gray-500 text-center">
        No results found
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

interface Option {
  [key: string]: any
}

const props = withDefaults(
  defineProps<{
    modelValue: string | number | null
    options: Option[]
    labelKey?: string
    valueKey?: string
    placeholder?: string
    required?: boolean
    disabled?: boolean
    id?: string
    inputClass?: string
    searchFunction?: (query: string) => Promise<void>
    debounceMs?: number
  }>(),
  {
    labelKey: 'name',
    valueKey: 'id',
    placeholder: 'Search...',
    required: false,
    disabled: false,
    id: 'searchable-dropdown',
    inputClass: '',
    debounceMs: 300
  }
)

const emit = defineEmits<{
  'update:modelValue': [value: string | number | null]
  'change': [option: Option | null]
}>()

// Reactive state
const searchQuery = ref('')
const isOpen = ref(false)
const highlightedIndex = ref(0)
const dropdownRef = ref<HTMLElement | null>(null)
const loading = ref(false)
const debounceTimer = ref<number | null>(null)

// Computed
const filteredOptions = computed(() => {
  if (!searchQuery.value) {
    return props.options
  }
  
  const query = searchQuery.value.toLowerCase()
  return props.options.filter(option => {
    const label = String(option[props.labelKey] || '').toLowerCase()
    return label.includes(query)
  })
})

const selectedOption = computed(() => {
  return props.options.find(option => option[props.valueKey] === props.modelValue)
})

// Methods
const handleFocus = () => {
  if (!props.disabled) {
    isOpen.value = true
    highlightedIndex.value = 0
  }
}

const handleInput = () => {
  isOpen.value = true
  highlightedIndex.value = 0
  
  // If user clears the input, clear the selection
  if (!searchQuery.value) {
    emit('update:modelValue', null)
    emit('change', null)
    return
  }

  // If searchFunction is provided, use debounced API search
  if (props.searchFunction) {
    // Clear existing timer
    if (debounceTimer.value) {
      clearTimeout(debounceTimer.value)
    }

    // Set new timer for debounced search
    debounceTimer.value = window.setTimeout(async () => {
      await performSearch()
    }, props.debounceMs)
  }
}

const performSearch = async () => {
  if (!props.searchFunction || !searchQuery.value) return

  loading.value = true
  try {
    // The parent component will handle updating the options
    await props.searchFunction(searchQuery.value)
  } catch (error) {
    console.error('Search error:', error)
  } finally {
    loading.value = false
  }
}

const selectOption = (option: Option) => {
  searchQuery.value = option[props.labelKey]
  emit('update:modelValue', option[props.valueKey])
  emit('change', option)
  closeDropdown()
}

const selectHighlighted = () => {
  if (filteredOptions.value.length > 0 && highlightedIndex.value >= 0) {
    selectOption(filteredOptions.value[highlightedIndex.value])
  }
}

const navigateDown = () => {
  if (highlightedIndex.value < filteredOptions.value.length - 1) {
    highlightedIndex.value++
  }
}

const navigateUp = () => {
  if (highlightedIndex.value > 0) {
    highlightedIndex.value--
  }
}

const clearSelection = () => {
  searchQuery.value = ''
  emit('update:modelValue', null)
  emit('change', null)
  isOpen.value = true
}

const closeDropdown = () => {
  isOpen.value = false
}

// Click outside handler
const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    closeDropdown()
  }
}

// Watch for external value changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      const option = props.options.find(opt => opt[props.valueKey] === newValue)
      if (option) {
        searchQuery.value = option[props.labelKey]
      }
    } else {
      searchQuery.value = ''
    }
  },
  { immediate: true }
)

// Watch for options changes
watch(
  () => props.options,
  () => {
    // Update search query if selected option label changed
    if (props.modelValue && selectedOption.value) {
      searchQuery.value = selectedOption.value[props.labelKey]
    }
  }
)

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
