<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">Add Document</h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Error Message -->
        <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
          <p class="text-sm text-red-600">{{ error }}</p>
        </div>

        <!-- File Upload -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Upload File *
          </label>
          <FileUpload
            :multiple="false"
            :auto-upload="false"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            accept-text="PDF, DOC, DOCX, JPG, PNG up to 10MB"
            @files-selected="handleFilesSelected"
            @upload-success="handleFileUploadSuccess"
            @upload-error="handleFileUploadError"
          />
          <p v-if="!uploadedFile" class="text-sm text-red-600 mt-1">Please upload a file</p>
        </div>

        <!-- Remark -->
        <div>
          <label for="remark" class="block text-sm font-medium text-gray-700 mb-2">
            Remark
          </label>
          <textarea
            id="remark"
            v-model="form.remark"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter any additional notes about this document"
          ></textarea>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            :disabled="loading"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            :disabled="loading || !uploadedFile"
          >
            {{ loading ? 'Uploading...' : 'Add Document' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CustomerDocumentService } from '@/services/api'
import type { CreateCustomerDocumentRequest, File as FileType } from '@/types'
import FileUpload from '@/components/FileUpload.vue'

interface Props {
  customerId: string
}

interface Emits {
  (e: 'close'): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Reactive data
const loading = ref(false)
const error = ref<string | null>(null)
const uploadedFile = ref<FileType | null>(null)

const form = ref({
  remark: ''
})

// Methods
const handleFilesSelected = (files: File[]) => {
  // Reset uploaded file when new files are selected
  uploadedFile.value = null
  error.value = null
}

const handleFileUploadSuccess = (files: FileType[]) => {
  if (files.length > 0) {
    uploadedFile.value = files[0]
    error.value = null
  }
}

const handleFileUploadError = (errorMessage: string) => {
  error.value = `File upload failed: ${errorMessage}`
  uploadedFile.value = null
}

const handleSubmit = async () => {
  if (!uploadedFile.value) {
    error.value = 'Please upload a file first'
    return
  }

  loading.value = true
  error.value = null

  try {
    // Create document with uploaded file
    const documentData: CreateCustomerDocumentRequest = {
      customerId: props.customerId,
      fileId: uploadedFile.value.id,
      docType: 4, // Default to "Other" type
      name: uploadedFile.value.originalName,
      remark: form.value.remark || undefined,
      status: 1 // Active
    }

    const response = await CustomerDocumentService.createCustomerDocument(documentData)
    if (response.success) {
      emit('success')
      emit('close')
    } else {
      error.value = response.error || 'Failed to create document'
    }
  } catch (err) {
    error.value = 'Failed to create document'
    console.error('Create document error:', err)
  } finally {
    loading.value = false
  }
}
</script>
