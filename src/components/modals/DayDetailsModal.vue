<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">
            {{ formatDate(day.date) }}
          </h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div v-if="day.items.length === 0" class="text-gray-500 text-center py-8">
          No events on this day
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="item in day.items"
            :key="item.id"
            class="p-3 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors"
            :class="{
              'border-blue-200 bg-blue-50': item.color === 'blue',
              'border-red-200 bg-red-50': item.color === 'red',
              'border-green-200 bg-green-50': item.color === 'green'
            }"
            @click="showItemDetails(item)"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <div
                    class="w-3 h-3 rounded-full"
                    :class="{
                      'bg-blue-500': item.color === 'blue',
                      'bg-red-500': item.color === 'red',
                      'bg-green-500': item.color === 'green'
                    }"
                  ></div>
                  <h4 class="text-sm font-medium text-gray-900">{{ item.title }}</h4>
                </div>
                <p class="text-xs text-gray-500 mt-1 capitalize">{{ item.type.replace('-', ' ') }}</p>
              </div>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>

        <div class="flex justify-end pt-4">
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { CalendarItem } from '@/types'

interface CalendarDay {
  date: Date
  isCurrentMonth: boolean
  isToday: boolean
  items: CalendarItem[]
}

defineProps<{
  day: CalendarDay
}>()

const emit = defineEmits<{
  close: []
  showItem: [item: CalendarItem]
}>()

const formatDate = (date: Date) => {
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const showItemDetails = (item: CalendarItem) => {
  emit('showItem', item)
  emit('close')
}
</script>
