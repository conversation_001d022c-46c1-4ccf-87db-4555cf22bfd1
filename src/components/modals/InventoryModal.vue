<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Inventory' : 'Add Inventory' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Customer -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Customer *</label>
              <select
                v-model="form.customerId"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Customer</option>
                <option v-for="customer in customers" :key="customer.id" :value="customer.id">
                  {{ customer.name }}
                </option>
              </select>
            </div>

            <!-- Serial Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Serial Number</label>
              <input
                v-model="form.sn"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter serial number"
              />
            </div>

            <!-- Status -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Status *</label>
              <select
                v-model="form.status"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option :value="0">Inactive</option>
                <option :value="1">Active</option>
              </select>
            </div>

            <!-- Order ID -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Order ID</label>
              <input
                v-model="form.orderId"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter order ID"
              />
            </div>

            <!-- Stock In Date -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Stock In Date</label>
              <input
                v-model="form.stockinDate"
                type="date"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <!-- Stock In By -->
            <div>
              <label for="stockinBy" class="block text-sm font-medium text-gray-700 mb-2">Stock In By</label>
              <SearchableDropdown
                id="stockinBy"
                :model-value="form.stockinBy || null"
                :options="users"
                label-key="name"
                value-key="id"
                placeholder="Search user..."
                @update:model-value="(value) => form.stockinBy = value ? String(value) : undefined"
              />
            </div>

            <!-- Stock Out Date -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Stock Out Date</label>
              <input
                v-model="form.stockoutDate"
                type="date"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <!-- Stock Out By -->
            <div>
              <label for="stockoutBy" class="block text-sm font-medium text-gray-700 mb-2">Stock Out By</label>
              <SearchableDropdown
                id="stockoutBy"
                :model-value="form.stockoutBy || null"
                :options="users"
                label-key="name"
                value-key="id"
                placeholder="Search user..."
                @update:model-value="(value) => form.stockoutBy = value ? String(value) : undefined"
              />
            </div>

            <!-- Warranty Period (months) -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Warranty Period (months)</label>
              <input
                v-model.number="form.warrantyPeriod"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter warranty period"
              />
            </div>

            <!-- Warranty Start Date -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Warranty Start Date</label>
              <input
                v-model="form.warrantyStartDate"
                type="date"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <!-- Warranty Expiry Date -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Warranty Expiry Date</label>
              <input
                v-model="form.warrantyExpDate"
                type="date"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <!-- Maintenance Vendor ID -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Maintenance Vendor ID</label>
              <input
                v-model="form.maintenanceVendorId"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter maintenance vendor ID"
              />
            </div>

            <!-- Maintenance Expired -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Maintenance Expired</label>
              <input
                v-model="form.maintenanceExpired"
                type="date"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <!-- Order Stage ID -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Order Stage ID</label>
              <input
                v-model="form.orderStageId"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter order stage ID"
              />
            </div>

            <!-- Order Stage 2 Equipment ID -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Order Stage 2 Equipment ID</label>
              <input
                v-model="form.orderStage2EquipmentId"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter order stage 2 equipment ID"
              />
            </div>
          </div>

          <!-- Remark -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Remark</label>
            <textarea
              v-model="form.remark"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter remarks"
            ></textarea>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error</h3>
                <div class="mt-2 text-sm text-red-700">{{ error }}</div>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {{ loading ? (isEditMode ? 'Updating...' : 'Creating...') : (isEditMode ? 'Update Inventory' : 'Create Inventory') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { EquipmentInventoryService, CustomerService, UserService } from '@/services/api'
import type { EquipmentInventory, CreateEquipmentInventoryRequest, UpdateEquipmentInventoryRequest, Customer, UserResponse } from '@/types'
import SearchableDropdown from '@/components/SearchableDropdown.vue'

// Props
const props = defineProps<{
  inventory?: EquipmentInventory
  equipmentId: string
}>()

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Computed
const isEditMode = computed(() => !!props.inventory)

// Reactive data
const customers = ref<Customer[]>([])
const users = ref<UserResponse[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const form = ref<CreateEquipmentInventoryRequest | UpdateEquipmentInventoryRequest>({
  equipmentId: props.equipmentId,
  customerId: '',
  orderId: '',
  sn: '',
  status: 1,
  remark: '',
  stockinDate: '',
  stockinBy: '',
  stockoutDate: '',
  stockoutBy: '',
  warrantyPeriod: undefined,
  warrantyStartDate: '',
  warrantyExpDate: '',
  orderStageId: '',
  orderStage2EquipmentId: '',
  maintenanceVendorId: '',
  maintenanceExpired: ''
})

// Methods
const handleSubmit = async () => {
  loading.value = true
  error.value = null

  try {
    // Clean up empty strings to undefined and ensure proper typing
    const cleanedFormEntries = Object.entries(form.value).map(([key, value]) => [key, value === '' ? undefined : value])
    const cleanedForm = Object.fromEntries(cleanedFormEntries)

    let response
    if (isEditMode.value && props.inventory) {
      // For update, we can use partial fields
      const updateData: UpdateEquipmentInventoryRequest = {
        ...cleanedForm,
        equipmentId: cleanedForm.equipmentId || props.equipmentId,
        customerId: cleanedForm.customerId || form.value.customerId,
        status: cleanedForm.status !== undefined ? cleanedForm.status : form.value.status
      }
      response = await EquipmentInventoryService.updateEquipmentInventory(
        props.inventory.id,
        updateData
      )
    } else {
      // For create, ensure required fields are present
      const createData: CreateEquipmentInventoryRequest = {
        equipmentId: cleanedForm.equipmentId || props.equipmentId,
        customerId: cleanedForm.customerId || form.value.customerId,
        status: cleanedForm.status !== undefined ? cleanedForm.status : form.value.status,
        orderId: cleanedForm.orderId,
        sn: cleanedForm.sn,
        remark: cleanedForm.remark,
        stockinDate: cleanedForm.stockinDate,
        stockinBy: cleanedForm.stockinBy,
        stockoutDate: cleanedForm.stockoutDate,
        stockoutBy: cleanedForm.stockoutBy,
        warrantyPeriod: cleanedForm.warrantyPeriod,
        warrantyStartDate: cleanedForm.warrantyStartDate,
        warrantyExpDate: cleanedForm.warrantyExpDate,
        orderStageId: cleanedForm.orderStageId,
        orderStage2EquipmentId: cleanedForm.orderStage2EquipmentId,
        maintenanceVendorId: cleanedForm.maintenanceVendorId,
        maintenanceExpired: cleanedForm.maintenanceExpired
      }
      response = await EquipmentInventoryService.createEquipmentInventory(
        createData
      )
    }

    if (response.success) {
      emit('success')
      emit('close')
    } else {
      const action = isEditMode.value ? 'update' : 'create'
      error.value = response.error || `Failed to ${action} inventory`
    }
  } catch (err) {
    const action = isEditMode.value ? 'update' : 'create'
    error.value = `Failed to ${action} inventory`
    console.error(`${action} inventory error:`, err)
  } finally {
    loading.value = false
  }
}

const loadCustomers = async () => {
  try {
    const response = await CustomerService.getCustomers({ limit: 100, page: 1 })
    if (response.success && response.data?.success && response.data.data) {
      const paginatedCustomers = response.data.data
      customers.value = paginatedCustomers.data
    }
  } catch (err) {
    console.error('Load customers error:', err)
  }
}

const loadUsers = async () => {
  try {
    const response = await UserService.getUsers({ limit: 100, page: 1 })
    if (response.success && response.data?.success && response.data.data) {
      const paginatedUsers = response.data.data
      users.value = paginatedUsers.data
    }
  } catch (err) {
    console.error('Load users error:', err)
  }
}

const initializeForm = () => {
  if (isEditMode.value && props.inventory) {
    form.value = {
      equipmentId: props.inventory.equipmentId,
      customerId: props.inventory.customerId,
      orderId: props.inventory.orderId || '',
      sn: props.inventory.sn || '',
      status: props.inventory.status,
      remark: props.inventory.remark || '',
      stockinDate: props.inventory.stockinDate ? formatDateForInput(props.inventory.stockinDate) : '',
      stockinBy: props.inventory.stockinBy || '',
      stockoutDate: props.inventory.stockoutDate ? formatDateForInput(props.inventory.stockoutDate) : '',
      stockoutBy: props.inventory.stockoutBy || '',
      warrantyPeriod: props.inventory.warrantyPeriod,
      warrantyStartDate: props.inventory.warrantyStartDate ? formatDateForInput(props.inventory.warrantyStartDate) : '',
      warrantyExpDate: props.inventory.warrantyExpDate ? formatDateForInput(props.inventory.warrantyExpDate) : '',
      orderStageId: props.inventory.orderStageId || '',
      orderStage2EquipmentId: props.inventory.orderStage2EquipmentId || '',
      maintenanceVendorId: props.inventory.maintenanceVendorId || '',
      maintenanceExpired: props.inventory.maintenanceExpired ? formatDateForInput(props.inventory.maintenanceExpired) : ''
    }
  } else {
    // Reset form for add mode
    form.value = {
      equipmentId: props.equipmentId,
      customerId: '',
      orderId: '',
      sn: '',
      status: 1,
      remark: '',
      stockinDate: '',
      stockinBy: '',
      stockoutDate: '',
      stockoutBy: '',
      warrantyPeriod: undefined,
      warrantyStartDate: '',
      warrantyExpDate: '',
      orderStageId: '',
      orderStage2EquipmentId: '',
      maintenanceVendorId: '',
      maintenanceExpired: ''
    }
  }
}

const formatDateForInput = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    return date.toISOString().split('T')[0]
  } catch {
    return ''
  }
}

// Watch for prop changes
watch(() => [props.inventory, props.equipmentId], initializeForm, { immediate: true })

onMounted(() => {
  loadCustomers()
  loadUsers()
})
</script>
