<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Customer Equipment' : 'Add Customer Equipment' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Equipment Selection -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Equipment Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="equipmentId" class="block text-sm font-medium text-gray-700 mb-1">Equipment *</label>
                <SearchableDropdown
                  id="equipmentId"
                  v-model="form.equipmentId"
                  :options="equipmentList"
                  label-key="name"
                  value-key="id"
                  placeholder="Search for equipment..."
                  :required="true"
                  :search-function="searchEquipment"
                  @change="handleEquipmentChange"
                />
              </div>
              <div>
                <label for="sn" class="block text-sm font-medium text-gray-700 mb-1">Serial Number</label>
                <input
                  id="sn"
                  v-model="form.sn"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter serial number"
                />
              </div>
              <div>
                <label for="orderId" class="block text-sm font-medium text-gray-700 mb-1">Order ID</label>
                <input
                  id="orderId"
                  v-model="form.orderId"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter order ID"
                />
              </div>
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                <select
                  id="status"
                  v-model.number="form.status"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option :value="1">Active</option>
                  <option :value="0">Inactive</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Warranty Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Warranty Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="warrantyPeriod" class="block text-sm font-medium text-gray-700 mb-1">Warranty Period (months)</label>
                <input
                  id="warrantyPeriod"
                  v-model.number="form.warrantyPeriod"
                  type="number"
                  min="0"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter warranty period"
                />
              </div>
              <div>
                <label for="warrantyStartDate" class="block text-sm font-medium text-gray-700 mb-1">Warranty Start Date</label>
                <input
                  id="warrantyStartDate"
                  v-model="form.warrantyStartDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="warrantyExpDate" class="block text-sm font-medium text-gray-700 mb-1">Warranty Expiry Date</label>
                <input
                  id="warrantyExpDate"
                  v-model="form.warrantyExpDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          <!-- Stock Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Stock Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="stockinDate" class="block text-sm font-medium text-gray-700 mb-1">Stock In Date</label>
                <input
                  id="stockinDate"
                  v-model="form.stockinDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="stockinBy" class="block text-sm font-medium text-gray-700 mb-1">Stock In By</label>
                <input
                  id="stockinBy"
                  v-model="form.stockinBy"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter person who stocked in"
                />
              </div>
              <div>
                <label for="stockoutDate" class="block text-sm font-medium text-gray-700 mb-1">Stock Out Date</label>
                <input
                  id="stockoutDate"
                  v-model="form.stockoutDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="stockoutBy" class="block text-sm font-medium text-gray-700 mb-1">Stock Out By</label>
                <input
                  id="stockoutBy"
                  v-model="form.stockoutBy"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter person who stocked out"
                />
              </div>
            </div>
          </div>

          <!-- Maintenance Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Maintenance Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="maintenanceVendorId" class="block text-sm font-medium text-gray-700 mb-1">Maintenance Vendor</label>
                <SearchableDropdown
                  id="maintenanceVendorId"
                  v-model="form.maintenanceVendorId"
                  :options="vendors"
                  label-key="name"
                  value-key="id"
                  placeholder="Search for a vendor..."
                  :search-function="searchVendors"
                  @change="handleVendorChange"
                />
              </div>
              <div>
                <label for="maintenanceExpired" class="block text-sm font-medium text-gray-700 mb-1">Maintenance Expiry Date</label>
                <input
                  id="maintenanceExpired"
                  v-model="form.maintenanceExpired"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Additional Information</h4>
            <div>
              <label for="remark" class="block text-sm font-medium text-gray-700 mb-1">Remarks</label>
              <textarea
                id="remark"
                v-model="form.remark"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter any additional remarks"
              ></textarea>
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
            >
              {{ isEditMode ? 'Update Equipment' : 'Add Equipment' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { EquipmentInventoryService, EquipmentService, VendorService } from '@/services/api'
import type { EquipmentInventory, Equipment, Vendor } from '@/types'
import SearchableDropdown from '@/components/SearchableDropdown.vue'

const props = defineProps<{
  equipment?: EquipmentInventory
  customerId: string
}>()

const emit = defineEmits<{
  close: []
  success: []
}>()

const isEditMode = computed(() => !!props.equipment)

// Equipment and vendors data from API
const equipmentList = ref<Equipment[]>([])
const vendors = ref<Vendor[]>([])
const loadingEquipment = ref(false)
const loadingVendors = ref(false)

const form = ref({
  equipmentId: '',
  customerId: props.customerId,
  orderId: '',
  sn: '',
  status: 1,
  remark: '',
  stockinDate: '',
  stockinBy: '',
  stockoutDate: '',
  stockoutBy: '',
  warrantyPeriod: undefined as number | undefined,
  warrantyStartDate: '',
  warrantyExpDate: '',
  orderStageId: '',
  orderStage2EquipmentId: '',
  maintenanceVendorId: '',
  maintenanceExpired: ''
})

// Load equipment from API
const loadEquipment = async (search?: string) => {
  loadingEquipment.value = true
  try {
    const response = await EquipmentService.getEquipment({ 
      limit: 10,
      search: search || undefined
    })
    if (response.success && response.data?.data) {
      equipmentList.value = response.data.data.data
    }
  } catch (error) {
    console.error('Failed to load equipment:', error)
  } finally {
    loadingEquipment.value = false
  }
}

// Search equipment with debouncing handled by SearchableDropdown
const searchEquipment = async (query: string) => {
  await loadEquipment(query)
}

// Handle equipment selection
const handleEquipmentChange = (equipment: any) => {
  if (equipment) {
    form.value.equipmentId = equipment.id
  }
}

// Load vendors from API
const loadVendors = async (search?: string) => {
  loadingVendors.value = true
  try {
    const response = await VendorService.getVendors({ 
      limit: 10,
      search: search || undefined
    })
    if (response.success && response.data?.data) {
      vendors.value = response.data.data.data
    }
  } catch (error) {
    console.error('Failed to load vendors:', error)
  } finally {
    loadingVendors.value = false
  }
}

// Search vendors with debouncing handled by SearchableDropdown
const searchVendors = async (query: string) => {
  await loadVendors(query)
}

// Handle vendor selection
const handleVendorChange = (vendor: any) => {
  if (vendor) {
    form.value.maintenanceVendorId = vendor.id
  }
}

const handleSubmit = async () => {
  try {
    const equipmentInventoryData = {
      equipmentId: form.value.equipmentId,
      customerId: form.value.customerId,
      orderId: form.value.orderId || undefined,
      sn: form.value.sn || undefined,
      status: form.value.status,
      remark: form.value.remark || undefined,
      stockinDate: form.value.stockinDate || undefined,
      stockinBy: form.value.stockinBy || undefined,
      stockoutDate: form.value.stockoutDate || undefined,
      stockoutBy: form.value.stockoutBy || undefined,
      warrantyPeriod: form.value.warrantyPeriod,
      warrantyStartDate: form.value.warrantyStartDate || undefined,
      warrantyExpDate: form.value.warrantyExpDate || undefined,
      orderStageId: form.value.orderStageId || undefined,
      orderStage2EquipmentId: form.value.orderStage2EquipmentId || undefined,
      maintenanceVendorId: form.value.maintenanceVendorId || undefined,
      maintenanceExpired: form.value.maintenanceExpired || undefined
    }

    let response
    if (isEditMode.value && props.equipment) {
      response = await EquipmentInventoryService.updateEquipmentInventory(props.equipment.id, equipmentInventoryData)
    } else {
      response = await EquipmentInventoryService.createEquipmentInventory(equipmentInventoryData)
    }

    if (response.success && response.data && response.data.success) {
      emit('success')
      emit('close')
    } else {
      const action = isEditMode.value ? 'update' : 'add'
      alert(`Failed to ${action} equipment: ` + (response.error || 'Unknown error'))
    }
  } catch (error) {
    const action = isEditMode.value ? 'update' : 'add'
    alert(`Failed to ${action} equipment: ` + (error instanceof Error ? error.message : 'Unknown error'))
  }
}

// Initialize form with equipment data when in edit mode
onMounted(async () => {
  if (isEditMode.value && props.equipment) {
    // First, set the form values
    form.value = {
      equipmentId: props.equipment.equipmentId,
      customerId: props.equipment.customerId,
      orderId: props.equipment.orderId || '',
      sn: props.equipment.sn || '',
      status: props.equipment.status,
      remark: props.equipment.remark || '',
      stockinDate: props.equipment.stockinDate ? new Date(props.equipment.stockinDate).toISOString().split('T')[0] : '',
      stockinBy: props.equipment.stockinBy || '',
      stockoutDate: props.equipment.stockoutDate ? new Date(props.equipment.stockoutDate).toISOString().split('T')[0] : '',
      stockoutBy: props.equipment.stockoutBy || '',
      warrantyPeriod: props.equipment.warrantyPeriod,
      warrantyStartDate: props.equipment.warrantyStartDate ? new Date(props.equipment.warrantyStartDate).toISOString().split('T')[0] : '',
      warrantyExpDate: props.equipment.warrantyExpDate ? new Date(props.equipment.warrantyExpDate).toISOString().split('T')[0] : '',
      orderStageId: props.equipment.orderStageId || '',
      orderStage2EquipmentId: props.equipment.orderStage2EquipmentId || '',
      maintenanceVendorId: props.equipment.maintenanceVendorId || '',
      maintenanceExpired: props.equipment.maintenanceExpired ? new Date(props.equipment.maintenanceExpired).toISOString().split('T')[0] : ''
    }
    
    // Then load equipment and vendors - the SearchableDropdown will update when they are loaded
    await Promise.all([loadEquipment(), loadVendors()])
  } else {
    // Load equipment and vendors first for new equipment inventory
    await Promise.all([loadEquipment(), loadVendors()])
    
    // Set default values for new equipment inventory
    const today = new Date().toISOString().split('T')[0]
    form.value.stockinDate = today

    // Set default warranty start date to today
    form.value.warrantyStartDate = today

    // Set default warranty period to 36 months and calculate expiry date
    form.value.warrantyPeriod = 36
    const warrantyExpiry = new Date()
    warrantyExpiry.setMonth(warrantyExpiry.getMonth() + 36)
    form.value.warrantyExpDate = warrantyExpiry.toISOString().split('T')[0]
  }
})
</script>
