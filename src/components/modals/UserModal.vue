<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit User' : 'Add New User' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
              <input
                v-model="form.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter full name"
              />
            </div>

            <!-- Login -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Login *</label>
              <input
                v-model="form.login"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter login username"
              />
            </div>

            <!-- Email -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
              <input
                v-model="form.email"
                type="email"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter email address"
              />
            </div>

            <!-- Contact Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Contact Number *</label>
              <input
                v-model="form.contactNo"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter contact number"
              />
            </div>

            <!-- Password -->
            <div :class="isEditMode ? 'md:col-span-2' : ''">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Password {{ isEditMode ? '' : '*' }}
              </label>
              <input
                v-model="form.password"
                type="password"
                :required="!isEditMode"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                :placeholder="isEditMode ? 'Leave blank to keep current password' : 'Enter password'"
              />
              <p v-if="isEditMode" class="mt-1 text-xs text-gray-500">Leave blank if you don't want to change the password</p>
            </div>

            <!-- Role -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Role *</label>
              <select
                v-model="form.role"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select role</option>
                <option value="admin">Admin</option>
                <option value="manager">Manager</option>
                <option value="user">User</option>
                <option value="technician">Technician</option>
              </select>
            </div>

            <!-- Notification -->
            <div :class="isEditMode ? '' : 'md:col-span-2'">
              <label class="flex items-center" :class="isEditMode ? 'pt-8' : ''">
                <input
                  v-model="form.notification"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">Enable email notifications</span>
              </label>
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
            >
              {{ isEditMode ? 'Update User' : 'Add User' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { UserService } from '@/services/api'
import type { UserResponse, CreateUserRequest, UpdateUserRequest } from '@/types'

// Props
const props = defineProps<{
  user?: UserResponse
}>()

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Reactive data
const loading = ref(false)
const error = ref<string | null>(null)

const form = ref<CreateUserRequest | UpdateUserRequest>({
  name: '',
  login: '',
  email: '',
  password: '',
  role: '',
  contactNo: '',
  notification: false
})

// Computed
const isEditMode = computed(() => !!props.user)

// Initialize form with user data if editing
onMounted(() => {
  if (props.user) {
    form.value = {
      name: props.user.name,
      login: props.user.login,
      email: props.user.email,
      role: props.user.role,
      contactNo: props.user.contactNo,
      notification: props.user.notification,
      password: '' // Leave password empty
    }
  }
})

// Methods
const handleSubmit = async () => {
  loading.value = true
  error.value = null

  try {
    let response
    if (isEditMode.value) {
      // Remove password from payload if it's empty
      const payload = { ...form.value }
      if (!payload.password) {
        delete payload.password
      }
      response = await UserService.updateUser(props.user!.id, payload as UpdateUserRequest)
    } else {
      response = await UserService.createUser(form.value as CreateUserRequest)
    }

    if (response.success) {
      emit('success')
      emit('close')
    } else {
      error.value = response.error || `Failed to ${isEditMode.value ? 'update' : 'create'} user`
    }
  } catch (err) {
    error.value = `Failed to ${isEditMode.value ? 'update' : 'create'} user`
    console.error(`${isEditMode.value ? 'Update' : 'Create'} user error:`, err)
  } finally {
    loading.value = false
  }
}
</script>
