<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">{{ document ? 'Edit Document' : 'Add Customer Document' }}</h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Error Message -->
        <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
          <p class="text-sm text-red-600">{{ error }}</p>
        </div>

        <!-- Customer Selection (only in create mode) -->
        <div v-if="!isEditMode">
          <label for="customerId" class="block text-sm font-medium text-gray-700 mb-2">
            Customer *
          </label>
          <select
            id="customerId"
            v-model="form.customerId"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select a customer</option>
            <option v-for="customer in customers" :key="customer.id" :value="customer.id">
              {{ customer.name }}
            </option>
          </select>
        </div>

        <!-- Document Name (only in create mode) -->
        <div v-if="!isEditMode">
          <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
            Document Name *
          </label>
          <input
            id="name"
            v-model="form.name"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter document name"
          />
        </div>

        <!-- Document Type (only in create mode) -->
        <div v-if="!isEditMode">
          <label for="docType" class="block text-sm font-medium text-gray-700 mb-2">
            Document Type *
          </label>
          <select
            id="docType"
            v-model="form.docType"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select document type</option>
            <option :value="1">Contract</option>
            <option :value="2">Invoice</option>
            <option :value="3">Certificate</option>
            <option :value="4">Other</option>
          </select>
        </div>

        <!-- Status (only in create mode) -->
        <div v-if="!isEditMode">
          <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
            Status *
          </label>
          <select
            id="status"
            v-model="form.status"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option :value="1">Active</option>
            <option :value="0">Inactive</option>
          </select>
        </div>

        <!-- Remark -->
        <div>
          <label for="remark" class="block text-sm font-medium text-gray-700 mb-2">
            Remark
          </label>
          <textarea
            id="remark"
            v-model="form.remark"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter any additional notes"
          ></textarea>
        </div>

        <!-- File Upload (only in create mode) -->
        <div v-if="!isEditMode">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Upload File *
          </label>
          <FileUpload
            :multiple="false"
            :auto-upload="false"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            accept-text="PDF, DOC, DOCX, JPG, PNG up to 10MB"
            @files-selected="handleFilesSelected"
            @upload-success="handleFileUploadSuccess"
            @upload-error="handleFileUploadError"
          />
          <p v-if="!uploadedFile" class="text-sm text-red-600 mt-1">Please upload a file</p>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            :disabled="loading"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            :disabled="loading || (!isEditMode && !uploadedFile)"
          >
            {{ loading ? (isEditMode ? 'Updating...' : 'Creating...') : (isEditMode ? 'Update Document' : 'Create Document') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { CustomerDocumentService, CustomerService } from '@/services/api'
import type { CreateCustomerDocumentRequest, Customer, File as FileType } from '@/types'
import FileUpload from '@/components/FileUpload.vue'

interface Props {
  customerId?: string
  document?: any
}

interface Emits {
  (e: 'close'): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Reactive data
const loading = ref(false)
const error = ref<string | null>(null)
const customers = ref<Customer[]>([])
const uploadedFile = ref<FileType | null>(null)

const form = ref<CreateCustomerDocumentRequest>({
  customerId: props.customerId || props.document?.customerId || '',
  fileId: props.document?.fileId || '',
  docType: props.document?.docType || 1,
  name: props.document?.name || '',
  remark: props.document?.remark || '',
  status: props.document?.status ?? 1
})

const isEditMode = ref(!!props.document)

// Methods
const loadCustomers = async () => {
  try {
    const response = await CustomerService.getCustomers({ limit: 100, page: 1 })
    if (response.success && response.data?.success && response.data.data) {
      const paginatedData = response.data.data
      customers.value = paginatedData.data
    }
  } catch (err) {
    console.error('Failed to load customers:', err)
  }
}

const handleFilesSelected = (files: File[]) => {
  // Reset uploaded file when new files are selected
  uploadedFile.value = null
  error.value = null
}

const handleFileUploadSuccess = (files: FileType[]) => {
  if (files.length > 0) {
    uploadedFile.value = files[0]
    form.value.fileId = files[0].id
    error.value = null
  }
}

const handleFileUploadError = (errorMessage: string) => {
  error.value = `File upload failed: ${errorMessage}`
  uploadedFile.value = null
}

const handleSubmit = async () => {
  if (!isEditMode.value && !uploadedFile.value) {
    error.value = 'Please upload a file first'
    return
  }

  loading.value = true
  error.value = null

  try {
    let response
    if (isEditMode.value) {
      // Only update remark in edit mode
      response = await CustomerDocumentService.updateCustomerDocument(props.document.id, {
        remark: form.value.remark
      })
    } else {
      response = await CustomerDocumentService.createCustomerDocument(form.value)
    }
    
    if (response.success) {
      emit('success')
      emit('close')
    } else {
      error.value = response.error || `Failed to ${isEditMode.value ? 'update' : 'create'} document`
    }
  } catch (err) {
    error.value = `Failed to ${isEditMode.value ? 'update' : 'create'} document`
    console.error(`${isEditMode.value ? 'Update' : 'Create'} document error:`, err)
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  if (!isEditMode.value) {
    loadCustomers()
  }
})
</script>
