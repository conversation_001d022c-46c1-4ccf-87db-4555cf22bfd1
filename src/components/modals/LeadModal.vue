<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Lead' : 'Add New Lead' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Basic Lead Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Basic Lead Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="leadNo" class="block text-sm font-medium text-gray-700 mb-1">Lead Number *</label>
                <input
                  id="leadNo"
                  v-model="form.leadNo"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter lead number"
                />
              </div>
              <div>
                <label for="companyName" class="block text-sm font-medium text-gray-700 mb-1">Company Name *</label>
                <input
                  id="companyName"
                  v-model="form.companyName"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter company name"
                />
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Contact Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="contactPerson" class="block text-sm font-medium text-gray-700 mb-1">Contact Person *</label>
                <input
                  id="contactPerson"
                  v-model="form.contactPerson"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter contact person name"
                />
              </div>
              <div>
                <label for="contactEmail" class="block text-sm font-medium text-gray-700 mb-1">Contact Email *</label>
                <input
                  id="contactEmail"
                  v-model="form.contactEmail"
                  type="email"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter contact email"
                />
              </div>
              <div>
                <label for="contactPhone" class="block text-sm font-medium text-gray-700 mb-1">Contact Phone *</label>
                <input
                  id="contactPhone"
                  v-model="form.contactPhone"
                  type="tel"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter contact phone"
                />
              </div>
              <div>
                <label for="alternatePhone" class="block text-sm font-medium text-gray-700 mb-1">Alternate Phone</label>
                <input
                  id="alternatePhone"
                  v-model="form.alternatePhone"
                  type="tel"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter alternate phone"
                />
              </div>
            </div>
          </div>

          <!-- Address Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Address Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="md:col-span-2">
                <label for="address1" class="block text-sm font-medium text-gray-700 mb-1">Address 1 *</label>
                <input
                  id="address1"
                  v-model="form.address1"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter address line 1"
                />
              </div>
              <div class="md:col-span-2">
                <label for="address2" class="block text-sm font-medium text-gray-700 mb-1">Address 2</label>
                <input
                  id="address2"
                  v-model="form.address2"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter address line 2"
                />
              </div>
              <div>
                <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City *</label>
                <input
                  id="city"
                  v-model="form.city"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter city"
                />
              </div>
              <div>
                <label for="state" class="block text-sm font-medium text-gray-700 mb-1">State *</label>
                <input
                  id="state"
                  v-model="form.state"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter state"
                />
              </div>
              <div>
                <label for="postcode" class="block text-sm font-medium text-gray-700 mb-1">Postcode *</label>
                <input
                  id="postcode"
                  v-model="form.postcode"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter postcode"
                />
              </div>
              <div>
                <label for="country" class="block text-sm font-medium text-gray-700 mb-1">Country *</label>
                <input
                  id="country"
                  v-model="form.country"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter country"
                />
              </div>
            </div>
          </div>

          <!-- Lead Classification -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Lead Classification</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="source" class="block text-sm font-medium text-gray-700 mb-1">Source *</label>
                <select
                  id="source"
                  v-model="form.source"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select source</option>
                  <option value="website">Website</option>
                  <option value="referral">Referral</option>
                  <option value="cold_call">Cold Call</option>
                  <option value="email_campaign">Email Campaign</option>
                  <option value="social_media">Social Media</option>
                  <option value="trade_show">Trade Show</option>
                  <option value="partner">Partner</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Priority *</label>
                <select
                  id="priority"
                  v-model="form.priority"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select priority</option>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                <select
                  id="status"
                  v-model="form.status"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select status</option>
                  <option value="new">New</option>
                  <option value="contacted">Contacted</option>
                  <option value="qualified">Qualified</option>
                  <option value="proposal">Proposal</option>
                  <option value="negotiation">Negotiation</option>
                  <option value="closed_won">Closed Won</option>
                  <option value="closed_lost">Closed Lost</option>
                  <option value="nurturing">Nurturing</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Service Requirements -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Service Requirements</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Services Required *</label>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                  <label v-for="service in serviceOptions" :key="service.value" class="flex items-center">
                    <input
                      type="checkbox"
                      :value="service.value"
                      v-model="form.servicesRequired"
                      class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span class="ml-2 text-sm text-gray-700">{{ service.label }}</span>
                  </label>
                </div>
              </div>
              <div>
                <label for="estimatedValue" class="block text-sm font-medium text-gray-700 mb-1">Estimated Value *</label>
                <input
                  id="estimatedValue"
                  v-model.number="form.estimatedValue"
                  type="number"
                  min="0"
                  step="0.01"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter estimated value"
                />
              </div>
              <div>
                <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">Currency *</label>
                <select
                  id="currency"
                  v-model="form.currency"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select currency</option>
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="MYR">MYR</option>
                  <option value="SGD">SGD</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Additional Information</h4>
            <div class="grid grid-cols-1 gap-4">
              <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  id="description"
                  v-model="form.description"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter lead description"
                ></textarea>
              </div>
              <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                <textarea
                  id="notes"
                  v-model="form.notes"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter additional notes"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ isEditMode ? 'Update Lead' : 'Create Lead' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { LeadService } from '@/services/api'
import type { Lead, LeadSource, LeadPriority, LeadStatus, ServiceType } from '@/types'

interface Props {
  lead?: Lead
}

interface Emits {
  (e: 'close'): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isEditMode = computed(() => !!props.lead)

const serviceOptions = [
  { value: 'isp', label: 'ISP' },
  { value: 'software', label: 'Software' },
  { value: 'cctv', label: 'CCTV' },
  { value: 'networking', label: 'Networking' },
  { value: 'installation', label: 'Installation' },
  { value: 'other', label: 'Other' }
]

const form = ref({
  leadNo: '',
  companyName: '',
  contactPerson: '',
  contactEmail: '',
  contactPhone: '',
  alternatePhone: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  postcode: '',
  country: '',
  source: '' as LeadSource,
  priority: '' as LeadPriority,
  status: '' as LeadStatus,
  servicesRequired: [] as ServiceType[],
  estimatedValue: 0,
  currency: '',
  description: '',
  notes: ''
})

const handleSubmit = async () => {
  try {
    const leadData = {
      leadNo: form.value.leadNo,
      companyName: form.value.companyName,
      contactPerson: form.value.contactPerson,
      contactEmail: form.value.contactEmail,
      contactPhone: form.value.contactPhone,
      alternatePhone: form.value.alternatePhone,
      address1: form.value.address1,
      address2: form.value.address2,
      city: form.value.city,
      state: form.value.state,
      postcode: form.value.postcode,
      country: form.value.country,
      source: form.value.source,
      priority: form.value.priority,
      status: form.value.status,
      servicesRequired: form.value.servicesRequired,
      estimatedValue: form.value.estimatedValue,
      currency: form.value.currency,
      description: form.value.description,
      notes: form.value.notes
    }

    let response
    if (isEditMode.value && props.lead) {
      response = await LeadService.updateLead(props.lead.id, leadData)
    } else {
      response = await LeadService.createLead(leadData)
    }

    if (response.success && response.data && response.data.success) {
      emit('success')
      emit('close')
    } else {
      const action = isEditMode.value ? 'update' : 'create'
      alert(`Failed to ${action} lead: ` + (response.error || 'Unknown error'))
    }
  } catch (error) {
    const action = isEditMode.value ? 'update' : 'create'
    alert(`Failed to ${action} lead: ` + (error instanceof Error ? error.message : 'Unknown error'))
  }
}

// Initialize form with lead data when in edit mode
onMounted(() => {
  if (isEditMode.value && props.lead) {
    form.value = {
      leadNo: props.lead.leadNo,
      companyName: props.lead.companyName,
      contactPerson: props.lead.contactPerson,
      contactEmail: props.lead.contactEmail,
      contactPhone: props.lead.contactPhone,
      alternatePhone: props.lead.alternatePhone || '',
      address1: props.lead.address1,
      address2: props.lead.address2 || '',
      city: props.lead.city,
      state: props.lead.state,
      postcode: props.lead.postcode,
      country: props.lead.country,
      source: props.lead.source,
      priority: props.lead.priority,
      status: props.lead.status,
      servicesRequired: props.lead.servicesRequired || [],
      estimatedValue: props.lead.estimatedValue || 0,
      currency: props.lead.currency,
      description: props.lead.description || '',
      notes: props.lead.notes || ''
    }
  }
})
</script>
