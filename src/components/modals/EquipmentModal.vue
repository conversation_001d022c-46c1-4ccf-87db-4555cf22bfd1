<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Equipment' : 'Add New Equipment' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Basic Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Basic Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Equipment Name *</label>
                <input
                  id="name"
                  v-model="form.name"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter equipment name"
                />
              </div>
              <div>
                <label for="sn" class="block text-sm font-medium text-gray-700 mb-1">Serial Number</label>
                <input
                  id="sn"
                  v-model="form.sn"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter serial number"
                />
              </div>
              <div>
                <label for="vendorId" class="block text-sm font-medium text-gray-700 mb-1">Vendor *</label>
                <SearchableDropdown
                  id="vendorId"
                  v-model="form.vendorId"
                  :options="vendors"
                  label-key="name"
                  value-key="id"
                  placeholder="Search for a vendor..."
                  :required="true"
                  :search-function="searchVendors"
                  @change="handleVendorChange"
                />
              </div>
              <!-- Order ID field hidden as per requirement -->
              <!-- <div>
                <label for="orderId" class="block text-sm font-medium text-gray-700 mb-1">Order ID</label>
                <input
                  id="orderId"
                  v-model="form.orderId"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter order ID"
                />
              </div> -->
            </div>
          </div>

          <!-- Category Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Category Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="category0" class="block text-sm font-medium text-gray-700 mb-1">Category 0 *</label>
                <input
                  id="category0"
                  v-model="form.category0"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter primary category"
                />
              </div>
              <div>
                <label for="category1" class="block text-sm font-medium text-gray-700 mb-1">Category 1 *</label>
                <input
                  id="category1"
                  v-model="form.category1"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter secondary category"
                />
              </div>
              <div>
                <label for="category2" class="block text-sm font-medium text-gray-700 mb-1">Category 2 *</label>
                <input
                  id="category2"
                  v-model="form.category2"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter tertiary category"
                />
              </div>
            </div>
          </div>

          <!-- Warranty Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Warranty Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="warrantyPeriod" class="block text-sm font-medium text-gray-700 mb-1">Warranty Period (months)</label>
                <input
                  id="warrantyPeriod"
                  v-model.number="form.warrantyPeriod"
                  type="number"
                  min="0"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter warranty period"
                />
              </div>
              <div>
                <label for="warrantyStartDate" class="block text-sm font-medium text-gray-700 mb-1">Warranty Start Date</label>
                <input
                  id="warrantyStartDate"
                  v-model="form.warrantyStartDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="warrantyExpDate" class="block text-sm font-medium text-gray-700 mb-1">Warranty Expiry Date</label>
                <input
                  id="warrantyExpDate"
                  v-model="form.warrantyExpDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          <!-- Status and Stock Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Status and Stock Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                <select
                  id="status"
                  v-model.number="form.status"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option :value="1">Active</option>
                  <option :value="2">Maintenance</option>
                  <option :value="3">Expired</option>
                  <option :value="0">Inactive</option>
                </select>
              </div>
              <div>
                <label for="stockinDate" class="block text-sm font-medium text-gray-700 mb-1">Stock In Date</label>
                <input
                  id="stockinDate"
                  v-model="form.stockinDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="stockinBy" class="block text-sm font-medium text-gray-700 mb-1">Stock In By</label>
                <input
                  id="stockinBy"
                  v-model="form.stockinBy"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter person who stocked in"
                />
              </div>
              <div>
                <label for="stockoutDate" class="block text-sm font-medium text-gray-700 mb-1">Stock Out Date</label>
                <input
                  id="stockoutDate"
                  v-model="form.stockoutDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="stockoutBy" class="block text-sm font-medium text-gray-700 mb-1">Stock Out By</label>
                <input
                  id="stockoutBy"
                  v-model="form.stockoutBy"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter person who stocked out"
                />
              </div>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Additional Information</h4>
            <div>
              <label for="remark" class="block text-sm font-medium text-gray-700 mb-1">Remarks</label>
              <textarea
                id="remark"
                v-model="form.remark"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter any additional remarks"
              ></textarea>
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
            >
              {{ isEditMode ? 'Update Equipment' : 'Add Equipment' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { EquipmentService, VendorService } from '@/services/api'
import type { Equipment, Vendor } from '@/types'
import SearchableDropdown from '@/components/SearchableDropdown.vue'

const props = defineProps<{
  equipment?: Equipment
}>()

const emit = defineEmits<{
  close: []
  success: []
}>()

const isEditMode = computed(() => !!props.equipment)

// Vendors data from API
const vendors = ref<Vendor[]>([])
const loadingVendors = ref(false)

const form = ref({
  name: '',
  vendorId: '',
  category0: '',
  category1: '',
  category2: '',
  orderId: '',
  sn: '',
  warrantyPeriod: undefined as number | undefined,
  warrantyStartDate: '',
  warrantyExpDate: '',
  status: 1,
  remark: '',
  stockinDate: '',
  stockinBy: '',
  stockoutDate: '',
  stockoutBy: ''
})

// Load vendors from API
const loadVendors = async (search?: string) => {
  loadingVendors.value = true
  try {
    const response = await VendorService.getVendors({ 
      limit: 100,
      search: search || undefined
    })
    if (response.success && response.data?.data) {
      const newVendors = response.data.data.data
      // Merge with existing vendors, avoiding duplicates
      const existingIds = new Set(vendors.value.map(v => v.id))
      const uniqueNewVendors = newVendors.filter(v => !existingIds.has(v.id))
      vendors.value = [...vendors.value, ...uniqueNewVendors]
    }
  } catch (error) {
    console.error('Failed to load vendors:', error)
  } finally {
    loadingVendors.value = false
  }
}

// Search vendors with debouncing handled by SearchableDropdown
const searchVendors = async (query: string) => {
  await loadVendors(query)
}

// Handle vendor selection
const handleVendorChange = (vendor: any) => {
  if (vendor) {
    form.value.vendorId = vendor.id
  }
}

const handleSubmit = async () => {
  try {
    const equipmentData = {
      name: form.value.name,
      vendorId: form.value.vendorId,
      category0: form.value.category0,
      category1: form.value.category1,
      category2: form.value.category2,
      orderId: form.value.orderId,
      sn: form.value.sn || undefined,
      warrantyPeriod: form.value.warrantyPeriod,
      warrantyStartDate: form.value.warrantyStartDate || undefined,
      warrantyExpDate: form.value.warrantyExpDate || undefined,
      status: form.value.status,
      remark: form.value.remark || undefined,
      stockinDate: form.value.stockinDate || undefined,
      stockinBy: form.value.stockinBy || undefined,
      stockoutDate: form.value.stockoutDate || undefined,
      stockoutBy: form.value.stockoutBy || undefined
    }

    let response
    if (isEditMode.value && props.equipment) {
      response = await EquipmentService.updateEquipment(props.equipment.id, equipmentData)
    } else {
      response = await EquipmentService.createEquipment(equipmentData)
    }

    if (response.success && response.data && response.data.success) {
      emit('success')
      emit('close')
    } else {
      const action = isEditMode.value ? 'update' : 'create'
      alert(`Failed to ${action} equipment: ` + (response.error || 'Unknown error'))
    }
  } catch (error) {
    const action = isEditMode.value ? 'update' : 'create'
    alert(`Failed to ${action} equipment: ` + (error instanceof Error ? error.message : 'Unknown error'))
  }
}

// Initialize form with equipment data when in edit mode
onMounted(async () => {
  if (isEditMode.value && props.equipment) {
    // First, set the form values
    form.value = {
      name: props.equipment.name,
      vendorId: props.equipment.vendorId,
      category0: props.equipment.category0,
      category1: props.equipment.category1,
      category2: props.equipment.category2,
      orderId: props.equipment.orderId,
      sn: props.equipment.sn || '',
      warrantyPeriod: props.equipment.warrantyPeriod,
      warrantyStartDate: props.equipment.warrantyStartDate ? new Date(props.equipment.warrantyStartDate).toISOString().split('T')[0] : '',
      warrantyExpDate: props.equipment.warrantyExpDate ? new Date(props.equipment.warrantyExpDate).toISOString().split('T')[0] : '',
      status: props.equipment.status,
      remark: props.equipment.remark || '',
      stockinDate: props.equipment.stockinDate ? new Date(props.equipment.stockinDate).toISOString().split('T')[0] : '',
      stockinBy: props.equipment.stockinBy || '',
      stockoutDate: props.equipment.stockoutDate ? new Date(props.equipment.stockoutDate).toISOString().split('T')[0] : '',
      stockoutBy: props.equipment.stockoutBy || ''
    }
    
    // Load the selected vendor first to ensure it's in the list
    if (props.equipment.vendorId) {
      try {
        const vendorResponse = await VendorService.getVendorById(props.equipment.vendorId)
        if (vendorResponse.success && vendorResponse.data?.data) {
          // Add the selected vendor to the vendors array
          vendors.value = [vendorResponse.data.data]
        }
      } catch (error) {
        console.error('Failed to load selected vendor:', error)
      }
    }
    
    // Then load more vendors for searching
    await loadVendors()
  } else {
    // Load vendors first for new equipment
    await loadVendors()
    
    // Set default values for new equipment
    const today = new Date().toISOString().split('T')[0]
    form.value.stockinDate = today

    // Set default warranty start date to today
    form.value.warrantyStartDate = today

    // Set default warranty period to 36 months and calculate expiry date
    form.value.warrantyPeriod = 36
    const warrantyExpiry = new Date()
    warrantyExpiry.setMonth(warrantyExpiry.getMonth() + 36)
    form.value.warrantyExpDate = warrantyExpiry.toISOString().split('T')[0]
  }
})
</script>
