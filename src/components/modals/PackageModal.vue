<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-[90]">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Package' : 'Add New Package' }}</h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div class="ml-3">
              <p class="text-sm text-red-800">{{ error }}</p>
            </div>
          </div>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 gap-6">
            <!-- Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
              <input
                v-model="form.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter package name"
              />
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ isEditMode ? 'Updating...' : 'Creating...' }}
              </span>
              <span v-else>{{ isEditMode ? 'Update Package' : 'Create Package' }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { PackageService } from '@/services/api'
import type { Package, CreatePackageRequest, UpdatePackageRequest } from '@/types'

// Props
const props = defineProps<{
  package?: Package
}>()

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Computed
const isEditMode = computed(() => !!props.package)

// Reactive data
const loading = ref(false)
const error = ref<string | null>(null)

const form = ref<CreatePackageRequest | UpdatePackageRequest>({
  name: ''
})

// Initialize form with package data
const initializeForm = () => {
  if (isEditMode.value && props.package) {
    form.value = {
      name: props.package.name
    }
  } else {
    form.value = {
      name: ''
    }
  }
}

// Watch for package changes
watch(() => props.package, initializeForm, { immediate: true })

// Methods
const handleSubmit = async () => {
  loading.value = true
  error.value = null

  try {
    let response
    if (isEditMode.value && props.package) {
      response = await PackageService.updatePackage(props.package.id, form.value as UpdatePackageRequest)
    } else {
      response = await PackageService.createPackage(form.value as CreatePackageRequest)
    }

    if (response.success) {
      emit('success')
      emit('close')
    } else {
      const action = isEditMode.value ? 'update' : 'create'
      error.value = response.error || `Failed to ${action} package`
    }
  } catch (err) {
    const action = isEditMode.value ? 'update' : 'create'
    error.value = `Failed to ${action} package`
    console.error(`${action} package error:`, err)
  } finally {
    loading.value = false
  }
}
</script>
