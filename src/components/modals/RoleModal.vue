<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Role' : 'Add New Role' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 gap-6">
            <!-- Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
              <input
                v-model="form.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter role name"
              />
            </div>

            <!-- Description -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                v-model="form.description"
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter role description"
              ></textarea>
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
            >
              {{ isEditMode ? 'Update Role' : 'Add Role' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { RoleService } from '@/services/api'
import type { Role, CreateRoleRequest, UpdateRoleRequest } from '@/types'

// Props
const props = defineProps<{
  role?: Role
}>()

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Reactive data
const loading = ref(false)
const error = ref<string | null>(null)

const form = ref<CreateRoleRequest | UpdateRoleRequest>({
  name: '',
  description: ''
})

// Computed
const isEditMode = computed(() => !!props.role)

// Initialize form with role data if editing
onMounted(() => {
  if (props.role) {
    form.value = {
      name: props.role.name,
      description: props.role.description || ''
    }
  }
})

// Methods
const handleSubmit = async () => {
  loading.value = true
  error.value = null

  try {
    const response = isEditMode.value
      ? await RoleService.updateRole(props.role!.id, form.value as UpdateRoleRequest)
      : await RoleService.createRole(form.value as CreateRoleRequest)

    if (response.success) {
      emit('success')
      emit('close')
    } else {
      error.value = response.error || `Failed to ${isEditMode.value ? 'update' : 'create'} role`
    }
  } catch (err) {
    error.value = `Failed to ${isEditMode.value ? 'update' : 'create'} role`
    console.error(`${isEditMode.value ? 'Update' : 'Create'} role error:`, err)
  } finally {
    loading.value = false
  }
}
</script>
