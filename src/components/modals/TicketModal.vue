<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Ticket' : 'Add New Ticket' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-4">
          <!-- Title -->
          <div>
            <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter ticket title"
            />
          </div>

          <!-- Description -->
          <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Describe the issue or request"
            ></textarea>
          </div>

          <!-- Customer and Level Row -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Customer -->
            <div>
              <label for="customer" class="block text-sm font-medium text-gray-700 mb-1">Customer *</label>
              <SearchableDropdown
                id="customer"
                v-model="form.customer"
                :options="customers"
                label-key="name"
                value-key="id"
                placeholder="Search for a customer..."
                :required="true"
                :search-function="searchCustomers"
              />
            </div>

            <!-- Level -->
            <div>
              <label for="level" class="block text-sm font-medium text-gray-700 mb-1">Priority Level</label>
              <select
                id="level"
                v-model="form.level"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select level</option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>

          <!-- Customer Package and Department Row -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Customer Package -->
            <div>
              <label for="customerPackage" class="block text-sm font-medium text-gray-700 mb-1">Customer Package</label>
              <select
                id="customerPackage"
                v-model="form.customerPackage"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select package (optional)</option>
                <option
                  v-for="pkg in filteredPackages"
                  :key="pkg.id"
                  :value="pkg.id"
                >
                  {{ pkg.servicesNo || pkg.circuitId || 'Package ' + pkg.id }}
                </option>
              </select>
            </div>

            <!-- Department -->
            <div>
              <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Department</label>
              <select
                id="department"
                v-model="form.department"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select department</option>
                <option value="technical">Technical</option>
                <option value="support">Support</option>
                <option value="sales">Sales</option>
                <option value="billing">Billing</option>
              </select>
            </div>
          </div>

          <!-- Status and Assignee Row -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Status -->
            <div>
              <label for="statustxt" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                id="statustxt"
                v-model="form.statustxt"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="open">Open</option>
                <option value="in-progress">In Progress</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
              </select>
            </div>

            <!-- Assignee -->
            <div>
              <label for="assignee" class="block text-sm font-medium text-gray-700 mb-1">Assignee</label>
              <input
                id="assignee"
                v-model="assigneeInput"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter assignee name(s), separated by commas"
              />
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="p-3 bg-red-50 border border-red-200 rounded-md">
            <p class="text-sm text-red-600">{{ error }}</p>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ isEditMode ? 'Update Ticket' : 'Add Ticket' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { TicketService, CustomerService, CustomerPackageService } from '@/services/api'
import type { Customer, CustomerPackage, CreateTicketRequest, Ticket } from '@/types'
import SearchableDropdown from '@/components/SearchableDropdown.vue'

const emit = defineEmits<{
  close: []
  created: []
  success: []
}>()

const props = defineProps<{
  ticket?: Ticket
}>()

// Reactive data
const customers = ref<Customer[]>([])
const customerPackages = ref<CustomerPackage[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const assigneeInput = ref('')

const form = ref({
  title: '',
  description: '',
  customer: '',
  level: '',
  customerPackage: '',
  department: '',
  assignee: [] as string[],
  statustxt: 'open'
})

// Initialize form with ticket data if in edit mode
const initializeForm = () => {
  if (isEditMode.value && props.ticket) {
    form.value = {
      title: props.ticket.title,
      description: props.ticket.description || '',
      customer: props.ticket.customer || '',
      level: props.ticket.level || '',
      customerPackage: props.ticket.customerPackage || '',
      department: props.ticket.department || '',
      assignee: props.ticket.assignee || [],
      statustxt: props.ticket.statustxt || 'open'
    }
    assigneeInput.value = props.ticket.assignee?.join(', ') || ''
  }
}

// Computed properties
const filteredPackages = computed(() => {
  if (!form.value.customer) return []
  return customerPackages.value.filter(pkg => pkg.customerId === form.value.customer)
})

const isEditMode = computed(() => !!props.ticket)

// Generate ticket number
const generateTicketNumber = (): string => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const time = String(now.getTime()).slice(-4)
  return `TKT${year}${month}${day}${time}`
}

// Handle form submission
const handleSubmit = async () => {
  loading.value = true
  error.value = null

  try {
    const assignees = assigneeInput.value
      ? assigneeInput.value.split(',').map(name => name.trim()).filter(name => name)
      : []

    const ticketData: CreateTicketRequest = {
      ticketno: isEditMode.value && props.ticket ? props.ticket.ticketno : generateTicketNumber(),
      title: form.value.title,
      description: form.value.description || undefined,
      customer: form.value.customer,
      level: form.value.level || undefined,
      customerPackage: form.value.customerPackage || undefined,
      department: form.value.department || undefined,
      statustxt: form.value.statustxt || undefined,
      assignee: assignees.length > 0 ? assignees : undefined
    }

    let response
    if (isEditMode.value && props.ticket) {
      response = await TicketService.updateTicket(props.ticket.id, ticketData)
    } else {
      response = await TicketService.createTicket(ticketData)
    }
    
    if (response.success && response.data && response.data.success) {
      emit('success')
      emit('close')
    } else {
      const action = isEditMode.value ? 'update' : 'create'
      alert(`Failed to ${action} ticket: ` + (response.error || 'Unknown error'))
    }
  } catch (err) {
    const action = isEditMode.value ? 'update' : 'create'
    alert(`Failed to ${action} ticket: ` + (error instanceof Error ? error.message : 'Unknown error'))
  } finally {
    loading.value = false
  }
}

// Search customers with API
const searchCustomers = async (query: string) => {
  try {
    const response = await CustomerService.getCustomers({ 
      limit: 15, 
      page: 1, 
      search: query 
    })

    if (response.success && response.data?.success && response.data.data) {
      const paginatedCustomers = response.data.data
      customers.value = paginatedCustomers.data
    }
  } catch (err) {
    console.error('Search customers error:', err)
  }
}

// Load data on mount
const loadData = async () => {
  try {
    const [customersResponse, packagesResponse] = await Promise.all([
      CustomerService.getCustomers({ limit: 15, page: 1 }),
      CustomerPackageService.getCustomerPackages({ limit: 50, page: 1 })
    ])

    if (customersResponse.success && customersResponse.data?.success && customersResponse.data.data) {
      const paginatedCustomers = customersResponse.data.data
      customers.value = paginatedCustomers.data
    }

    if (packagesResponse.success && packagesResponse.data?.success && packagesResponse.data.data) {
      const paginatedPackages = packagesResponse.data.data
      customerPackages.value = paginatedPackages.data
    }
  } catch (err) {
    console.error('Load data error:', err)
  }
}

onMounted(async () => {
  await loadData()
  initializeForm()
})
</script>
