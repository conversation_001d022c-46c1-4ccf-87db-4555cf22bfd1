<template>
  <!-- Mobile menu overlay -->
  <div
    v-if="mobileMenuOpen"
    class="fixed inset-0 bg-gray-600 bg-opacity-75 lg:hidden z-40"
    @click="closeMobileMenu"
  ></div>

  <!-- Sidebar -->
  <div
    class="fixed inset-y-0 left-0 z-[60] w-64 lg:w-72 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0"
    :class="{
      'translate-x-0': mobileMenuOpen,
      '-translate-x-full': !mobileMenuOpen
    }"
  >
    <div class="flex h-full flex-col">
      <!-- Logo -->
      <div class="flex h-16 md:h-18 shrink-0 items-center px-6 sm:px-8 border-b border-gray-200">
         <img src="/logo.png" class="w-full h-12"/>
      </div>

      <!-- Navigation -->
      <nav class="flex-1 px-6 md:px-8 py-6 space-y-1 overflow-y-auto">
        <template v-for="(item, index) in filteredNavigation" :key="item.name">
          <!-- Separator before admin-only items -->
          <div 
            v-if="item.adminOnly && (!filteredNavigation[index - 1]?.adminOnly)"
            class="border-t border-gray-200 my-3 pt-3"
          ></div>
          
          <router-link
            :to="item.href"
            @click="closeMobileMenu"
            class="group flex items-center px-4 md:px-5 py-3 text-sm md:text-base font-medium rounded-md transition-colors touch-manipulation ring-0"
            :class="[
              $route.path === item.href
                ? 'bg-blue-50 text-blue-700 border-r-2 md:border-r-3 border-blue-700'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 active:bg-gray-100'
            ]"
          >
          <svg v-if="item.name === 'Dashboard'" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <svg v-else-if="item.name === 'Customers'" xmlns="http://www.w3.org/2000/svg" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
          </svg>
          <svg v-else-if="item.name === 'Leads'" xmlns="http://www.w3.org/2000/svg" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <svg v-else-if="item.name === 'Deals'" xmlns="http://www.w3.org/2000/svg" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>

          <svg v-else-if="item.name === 'Customer Services'" xmlns="http://www.w3.org/2000/svg" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z" />
          </svg>
          <svg v-else-if="item.name === 'Packages'" xmlns="http://www.w3.org/2000/svg" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
          </svg>
          <svg v-else-if="item.name === 'Equipment'" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <svg v-else-if="item.name === 'Equipment Inventory'" xmlns="http://www.w3.org/2000/svg" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm-.375 5.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
          </svg>
          <svg v-else-if="item.name === 'Equipment Categories'" xmlns="http://www.w3.org/2000/svg" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 7.125C2.25 6.504 2.754 6 3.375 6h6c.621 0 1.125.504 1.125 1.125v3.75c0 .621-.504 1.125-1.125 1.125h-6a1.125 1.125 0 01-1.125-1.125v-3.75zM14.25 8.625c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v8.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 01-1.125-1.125v-8.25zM3.75 16.125c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 01-1.125-1.125v-2.25z" />
          </svg>
          <svg v-else-if="item.name === 'Tickets'" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
          </svg>
          <svg v-else-if="item.name === 'Files'" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <svg v-else-if="item.name === 'Roles'" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          <svg v-else-if="item.name === 'Users'" class="mr-3 md:mr-4 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
          {{ item.name }}
        </router-link>
        </template>
      </nav>

      <!-- User section -->
      <div class="border-t border-gray-200 px-6 md:px-8 py-4 md:py-6 flex-shrink-0">
        <div class="flex items-center justify-between">
          <div class="flex items-center min-w-0 flex-1">
            <div class="h-8 w-8 md:h-10 md:w-10 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
              <span class="text-sm md:text-base font-medium text-white">
                {{ currentUser?.name?.charAt(0).toUpperCase() || 'U' }}
              </span>
            </div>
            <div class="ml-3 md:ml-4 min-w-0 flex-1">
              <p class="text-sm md:text-base font-medium text-gray-900 truncate">{{ currentUser?.name || currentUser?.login || 'User' }}</p>
              <p class="text-xs md:text-sm lg:text-sm text-gray-500 truncate">{{ currentUser?.email || '<EMAIL>' }}</p>
            </div>
          </div>
          <button
            @click="handleLogout"
            class="p-1 md:p-2 lg:p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 flex-shrink-0 ml-2"
            title="Logout"
          >
            <svg class="h-5 w-5 md:h-5 md:w-5 lg:h-6 lg:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile header -->
  <div class="lg:hidden fixed top-0 left-0 right-0 z-30 bg-white shadow-sm border-b border-gray-200">
    <div class="flex items-center justify-between px-4 py-3">
       <img src="/logo.png" class="h-full w-24" />
      <button
        @click="toggleMobileMenu"
        class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 touch-manipulation flex-shrink-0"
        :aria-expanded="mobileMenuOpen"
        aria-label="Toggle navigation menu"
      >
        <svg
          class="h-6 w-6 transition-transform duration-200"
          :class="{ 'rotate-90': mobileMenuOpen }"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            v-if="!mobileMenuOpen"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"
          />
          <path
            v-else
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { logout, authState } from '@/utils/auth'

const router = useRouter()
const route = useRoute()
const mobileMenuOpen = ref(false)

// Get current user from auth state
const currentUser = computed(() => authState.currentUser.value)

// Check if user is admin or dev (case-insensitive)
const isAdminOrDev = computed(() => {
  const role = currentUser.value?.role?.toLowerCase()
  return role === 'admin' || role === 'dev'
})

// Navigation items
const navigation = [
  {
    name: 'Dashboard',
    href: '/',
    adminOnly: false
  },
  {
    name: 'Customers',
    href: '/customers',
    adminOnly: false
  },
  {
    name: 'Leads',
    href: '/leads',
    adminOnly: false
  },
  {
    name: 'Deals',
    href: '/deals',
    adminOnly: false
  },
  // {
  //   name: 'Customer Services',
  //   href: '/customer-services'
  // },
  {
    name: 'Equipment',
    href: '/equipment',
    adminOnly: false
  },
  // {
  //   name: 'Equipment Inventory',
  //   href: '/equipment-inventory'
  // },
  // {
  //   name: 'Equipment Categories',
  //   href: '/equipment-categories'
  // },
  {
    name: 'Tickets',
    href: '/tickets',
    adminOnly: false
  },
  // Admin-only section (grouped together)
  {
    name: 'Packages',
    href: '/packages',
    adminOnly: true
  },
  {
    name: 'Roles',
    href: '/roles',
    adminOnly: true
  },
  {
    name: 'Users',
    href: '/users',
    adminOnly: true
  }
  // {
  //   name: 'Files',
  //   href: '/files'
  // }
]

// Filter navigation based on user role
const filteredNavigation = computed(() => {
  return navigation.filter(item => !item.adminOnly || isAdminOrDev.value)
})

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

const handleLogout = async () => {
  try {
    await logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
    // Force logout even if API call fails
    router.push('/login')
  }
}

// Close mobile menu when route changes
watch(route, () => {
  closeMobileMenu()
})

// Handle keyboard events
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && mobileMenuOpen.value) {
    closeMobileMenu()
  }
}

// Add keyboard event listener
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

// Remove keyboard event listener
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

</script>
