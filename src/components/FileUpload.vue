<template>
  <div class="file-upload-container">
    <!-- Drag and Drop Area -->
    <div
      @drop="handleDrop"
      @dragover.prevent
      @dragenter.prevent
      @dragleave="handleDragLeave"
      :class="[
        'border-2 border-dashed rounded-lg p-6 text-center transition-colors',
        isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400',
        disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
      ]"
      @click="!disabled && triggerFileInput()"
    >
      <input
        ref="fileInput"
        type="file"
        :multiple="multiple"
        :accept="accept"
        @change="handleFileSelect"
        class="hidden"
        :disabled="disabled"
      />
      
      <div class="space-y-2">
        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
          <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <div class="text-sm text-gray-600">
          <span class="font-medium text-blue-600 hover:text-blue-500">Click to upload</span>
          or drag and drop
        </div>
        <p class="text-xs text-gray-500">
          {{ acceptText || 'PNG, JPG, PDF up to 10MB' }}
        </p>
      </div>
    </div>

    <!-- File List -->
    <div v-if="files.length > 0" class="mt-4 space-y-2">
      <div
        v-for="(file, index) in files"
        :key="index"
        class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
      >
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div class="min-w-0 flex-1">
            <p class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>
            <p class="text-sm text-gray-500">{{ formatFileSize(file.size) }}</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-2">
          <!-- Upload Progress -->
          <div v-if="file.uploading" class="flex items-center space-x-2">
            <div class="w-16 bg-gray-200 rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" :style="{ width: file.progress + '%' }"></div>
            </div>
            <span class="text-xs text-gray-500">{{ file.progress }}%</span>
          </div>
          
          <!-- Upload Status -->
          <div v-else-if="file.uploaded" class="text-green-600">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          
          <!-- Error Status -->
          <div v-else-if="file.error" class="text-red-600" :title="file.error">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          
          <!-- Remove Button -->
          <button
            @click="removeFile(index)"
            class="text-gray-400 hover:text-red-600 transition-colors"
            :disabled="file.uploading"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Upload Button -->
    <div v-if="files.length > 0 && !autoUpload" class="mt-4">
      <button
        @click="uploadFiles"
        :disabled="uploading || files.every(f => f.uploaded)"
        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        {{ uploading ? 'Uploading...' : 'Upload Files' }}
      </button>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
      <p class="text-sm text-red-600">{{ error }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { FileService } from '@/services/api'
import type { File as FileType } from '@/types'

interface FileWithStatus extends File {
  uploading?: boolean
  uploaded?: boolean
  progress?: number
  error?: string
  fileData?: FileType
}

interface Props {
  multiple?: boolean
  accept?: string
  acceptText?: string
  maxSize?: number // in bytes
  autoUpload?: boolean
  disabled?: boolean
}

interface Emits {
  (e: 'upload-success', files: FileType[]): void
  (e: 'upload-error', error: string): void
  (e: 'files-selected', files: File[]): void
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  accept: 'image/*,.pdf,.doc,.docx',
  maxSize: 10 * 1024 * 1024, // 10MB
  autoUpload: true,
  disabled: false
})

const emit = defineEmits<Emits>()

// Reactive data
const fileInput = ref<HTMLInputElement>()
const files = ref<FileWithStatus[]>([])
const isDragOver = ref(false)
const uploading = ref(false)
const error = ref<string | null>(null)

// Methods
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    addFiles(Array.from(target.files))
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  if (props.disabled) return
  
  const droppedFiles = Array.from(event.dataTransfer?.files || [])
  addFiles(droppedFiles)
}

const handleDragLeave = (event: DragEvent) => {
  // Only set isDragOver to false if we're leaving the drop zone entirely
  const currentTarget = event.currentTarget as Element
  const relatedTarget = event.relatedTarget as Node
  if (!currentTarget?.contains(relatedTarget)) {
    isDragOver.value = false
  }
}

const addFiles = (newFiles: File[]) => {
  error.value = null
  
  const validFiles = newFiles.filter(file => {
    // Check file size
    if (file.size > props.maxSize) {
      error.value = `File "${file.name}" is too large. Maximum size is ${formatFileSize(props.maxSize)}.`
      return false
    }
    return true
  })

  if (!props.multiple) {
    files.value = validFiles.map(file => Object.assign(file, {}))
  } else {
    files.value.push(...validFiles.map(file => Object.assign(file, {})))
  }

  emit('files-selected', validFiles)

  if (props.autoUpload && validFiles.length > 0) {
    uploadFiles()
  }
}

const removeFile = (index: number) => {
  files.value.splice(index, 1)
}

const uploadFiles = async () => {
  if (uploading.value) return
  
  uploading.value = true
  error.value = null
  
  const uploadPromises = files.value
    .filter(file => !file.uploaded && !file.uploading)
    .map(uploadSingleFile)
  
  try {
    await Promise.all(uploadPromises)
    const uploadedFiles = files.value
      .filter(file => file.uploaded && file.fileData)
      .map(file => file.fileData!)
    
    if (uploadedFiles.length > 0) {
      emit('upload-success', uploadedFiles)
    }
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Upload failed'
    error.value = errorMessage
    emit('upload-error', errorMessage)
  } finally {
    uploading.value = false
  }
}

const uploadSingleFile = async (file: FileWithStatus) => {
  file.uploading = true
  file.progress = 0
  file.error = undefined
  
  try {
    const formData = new FormData()
    formData.append('file', file)
    
    // Simulate progress (since we don't have real progress tracking)
    const progressInterval = setInterval(() => {
      if (file.progress! < 90) {
        file.progress = (file.progress || 0) + 10
      }
    }, 100)
    
    const response = await FileService.uploadFile(formData)
    
    clearInterval(progressInterval)
    
    if (response.success && response.data?.success && response.data.data) {
      file.progress = 100
      file.uploaded = true
      file.fileData = response.data.data
    } else {
      throw new Error(response.error || 'Upload failed')
    }
  } catch (err) {
    file.error = err instanceof Error ? err.message : 'Upload failed'
    throw err
  } finally {
    file.uploading = false
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.file-upload-container {
  @apply w-full;
}
</style>
