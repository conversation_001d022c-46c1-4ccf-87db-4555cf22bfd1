<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { isAuthenticated } from '@/utils/auth'
import AppLayout from '@/components/layout/AppLayout.vue'

const route = useRoute()

// Show layout only for authenticated routes
const showLayout = computed(() => {
  return route.meta.requiresAuth && isAuthenticated.value
})
</script>

<template>
  <div>
    <!-- Show layout for authenticated pages -->
    <AppLayout v-if="showLayout" />

    <!-- Show router view directly for login and other guest pages -->
    <router-view v-else />
  </div>
</template>

<style scoped></style>
