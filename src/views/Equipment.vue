<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Equipment</h1>
      <button
        @click="addEquipment"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Equipment
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 rounded-lg shadow">
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search equipment..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <select
          v-model="statusFilter"
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Status</option>
          <option value="1">Active</option>
          <option value="2">Maintenance</option>
          <option value="3">Expired</option>
          <option value="0">Inactive</option>
        </select>
        <button
          @click="clearFilters"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
        >
          Clear
        </button>
      </div>
    </div>

    <!-- Equipment Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-900">
          All Equipment ({{ pagination.state.total }})
        </h3>
      </div>

      <div v-if="equipment.length === 0" class="p-6 text-center text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <p class="mt-2">No equipment found</p>
        <p class="text-sm text-gray-400">Get started by adding your first equipment</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Brand
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Model
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Vendor
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Remark
              </th>
              <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                In Stock
              </th>
              <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Pre-Ordered
              </th>
              <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Delivered
              </th>
              <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Pending Install
              </th>
              <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                POC
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="equipmentItem in equipment"
              :key="equipmentItem.id"
              class="hover:bg-gray-50 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ equipmentItem.category0 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ equipmentItem.category1 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ equipmentItem.category2 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getVendorName(equipmentItem.vendorId) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ equipmentItem.remark ? equipmentItem.remark : '-' }}</div>
              </td>
              <td class="hidden px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ 0 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ 0 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ 0 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ 0 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ 0 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="editEquipment(equipmentItem)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded hover:bg-indigo-50"
                    title="Edit Equipment"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="viewEquipment(equipmentItem)"
                    class="text-blue-600 hover:text-blue-900 transition-colors p-1 rounded hover:bg-blue-50"
                    title="View Equipment"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0 1 18 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3 1.5 1.5 3-3.75" />
                    </svg>
                  </button>
                  <button
                    @click="deleteEquipment(equipmentItem)"
                    class="text-red-600 hover:text-red-900 transition-colors p-1 rounded hover:bg-red-50 hidden"
                    title="Delete Equipment"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <Pagination
        v-if="equipment.length > 0"
        :current-page="pagination.state.currentPage"
        :page-size="pagination.state.pageSize"
        :total="pagination.state.total"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- Equipment Modal -->
    <EquipmentModal
      v-if="showEquipmentModal"
      :equipment="selectedEquipment || undefined"
      @close="closeEquipmentModal"
      @success="handleEquipmentSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { CustomerService, EquipmentService, VendorService } from '@/services/api'
import type { Equipment, Customer, Vendor } from '@/types'
import EquipmentModal from '@/components/modals/EquipmentModal.vue'
import Pagination from '@/components/Pagination.vue'
import { usePagination } from '@/composables/usePagination'

// Router
const router = useRouter()

// Reactive data
const equipment = ref<Equipment[]>([])
const customers = ref<Customer[]>([])
const vendors = ref<Vendor[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const searchQuery = ref('')
const statusFilter = ref('')
const showEquipmentModal = ref(false)
const selectedEquipment = ref<Equipment | null>(null)

// Pagination setup
const pagination = usePagination({
  initialPageSize: 10,
  onPageChange: async () => {
    await loadData()
  }
})

// Watch for search and filter changes with debounce
let searchTimeout: number | null = null
watch([searchQuery, statusFilter], async () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(async () => {
    pagination.reset()
    await loadData()
  }, 300)
})

const getVendorName = (vendorId: string) => {
  const vendor = vendors.value.find(v => v.id === vendorId)
  return vendor?.name || 'N/A'
}

const getStatusText = (status: number) => {
  switch (status) {
    case 1: return 'Active'
    case 2: return 'Maintenance'
    case 3: return 'Expired'
    case 0: return 'Inactive'
    default: return 'Unknown'
  }
}

const getStatusClass = (status: number) => {
  switch (status) {
    case 1: return 'bg-green-100 text-green-800'
    case 2: return 'bg-yellow-100 text-yellow-800'
    case 3: return 'bg-red-100 text-red-800'
    case 0: return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}



const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const isExpired = (date: string | Date) => {
  return new Date(date) < new Date()
}

const isExpiringSoon = (date: string | Date) => {
  const now = new Date()
  const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
  const expiryDate = new Date(date)
  return expiryDate >= now && expiryDate <= thirtyDaysFromNow
}

const getExpiryStatus = (date: string | Date) => {
  if (isExpired(date)) {
    return 'Expired'
  } else if (isExpiringSoon(date)) {
    return 'Expires soon'
  }
  return 'Active'
}

const clearFilters = async () => {
  searchQuery.value = ''
  statusFilter.value = ''
  pagination.reset()
  await loadData()
}

// Pagination handlers
const handlePageChange = async (page: number) => {
  await pagination.goToPage(page)
}

const handlePageSizeChange = async (pageSize: number) => {
  await pagination.changePageSize(pageSize)
}

const viewEquipment = (equipment: Equipment) => {
  router.push({ name: 'equipment-detail', params: { id: equipment.id } })
}

const addEquipment = () => {
  selectedEquipment.value = null
  showEquipmentModal.value = true
}

const editEquipment = (equipment: Equipment) => {
  selectedEquipment.value = equipment
  showEquipmentModal.value = true
}

const deleteEquipment = async (equipmentItem: Equipment) => {
  if (confirm(`Are you sure you want to delete ${equipmentItem.name}?`)) {
    try {
      const response = await EquipmentService.deleteEquipment(equipmentItem.id)
      if (response.success) {
        // Reload data to reflect the changes
        await loadData()
      } else {
        error.value = response.error || 'Failed to delete equipment'
      }
    } catch (err) {
      error.value = 'Failed to delete equipment'
      console.error('Delete equipment error:', err)
    }
  }
}

const closeEquipmentModal = () => {
  showEquipmentModal.value = false
  selectedEquipment.value = null
}

const handleEquipmentSuccess = () => {
  closeEquipmentModal()
  loadData()
}

const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // Build query params with search and filters
    const params = pagination.getQueryParams()
    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    // Add status filter to params if needed
    // Note: This would require API support for status filtering

    // Load all data in parallel
    const [customersResponse, vendorsResponse, equipmentResponse] = await Promise.all([
      CustomerService.getCustomers({ limit: 100, page: 1 }), // Keep customers simple for now
      VendorService.getVendors({ limit: 100, page: 1 }),
      EquipmentService.getEquipment(params)
    ])

    // Handle customers response
    if (customersResponse.success && customersResponse.data?.success && customersResponse.data.data) {
      const paginatedCustomers = customersResponse.data.data
      customers.value = paginatedCustomers.data
    } else {
      customers.value = []
    }

    // Handle vendors response
    if (vendorsResponse.success && vendorsResponse.data?.success && vendorsResponse.data.data) {
      const paginatedVendors = vendorsResponse.data.data
      vendors.value = paginatedVendors.data
    } else {
      vendors.value = []
    }

    // Handle equipment response
    if (equipmentResponse.success && equipmentResponse.data?.success && equipmentResponse.data.data) {
      const paginatedEquipment = equipmentResponse.data.data
      equipment.value = paginatedEquipment.data
      pagination.updateFromResponse(paginatedEquipment)
    } else {
      equipment.value = []
      if (!equipmentResponse.success) {
        error.value = equipmentResponse.error || 'Failed to load equipment data'
      }
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load data'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>
