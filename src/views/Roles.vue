<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Roles</h1>
      <button
        @click="addRole"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Role
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 md:p-6 lg:p-8 rounded-lg shadow">
      <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search roles..."
            class="w-full px-3 md:px-4 lg:px-5 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <button
          @click="clearSearch"
          class="px-4 md:px-6 lg:px-8 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
        >
          Clear
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <div class="flex">
        <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <div class="ml-3">
          <p class="text-sm text-red-800">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Roles Table -->
    <div v-else class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-900">
          All Roles ({{ pagination.state.total }})
        </h3>
      </div>
      <div v-if="roles.length === 0" class="p-6 text-center text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <p class="mt-2">No roles found</p>
        <p class="text-sm text-gray-400">Get started by adding your first role</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="role in roles" :key="role.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ role.name }}</div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-500">{{ role.description || 'N/A' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ new Date(role.created_at).toLocaleDateString() }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-1">
                  <button
                    @click="editRole(role)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded hover:bg-indigo-50"
                    title="Edit Role"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteRole(role)"
                    class="text-red-600 hover:text-red-900 transition-colors p-1 rounded hover:bg-red-50"
                    title="Delete Role"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <Pagination
        v-if="roles.length > 0"
        :current-page="pagination.state.currentPage"
        :page-size="pagination.state.pageSize"
        :total="pagination.state.total"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- Modals -->
    <RoleModal
      v-if="showModal"
      :role="selectedRole || undefined"
      @close="closeModal"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { RoleService } from '@/services/api'
import type { Role } from '@/types'
import RoleModal from '@/components/modals/RoleModal.vue'
import Pagination from '@/components/Pagination.vue'
import { usePagination } from '@/composables/usePagination'

// Reactive data
const roles = ref<Role[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const searchQuery = ref('')

// Modal states
const showModal = ref(false)
const selectedRole = ref<Role | null>(null)

// Pagination setup
const pagination = usePagination({
  initialPageSize: 10,
  onPageChange: async () => {
    await loadRoles()
  }
})

// Watch for search query changes with debounce
let searchTimeout: number | null = null
watch(searchQuery, async () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(async () => {
    pagination.reset()
    await loadRoles()
  }, 300)
})

// Pagination handlers
const handlePageChange = async (page: number) => {
  await pagination.goToPage(page)
}

const handlePageSizeChange = async (pageSize: number) => {
  await pagination.changePageSize(pageSize)
}

const clearSearch = async () => {
  searchQuery.value = ''
  pagination.reset()
  await loadRoles()
}

// Methods
const loadRoles = async () => {
  loading.value = true
  error.value = null

  try {
    const params = pagination.getQueryParamsWithSearch(searchQuery.value)
    const response = await RoleService.getRoles(params)
    console.log(response)
    if (response.success && response.data?.success && response.data.data) {
      const paginatedRoles = response.data.data
      roles.value = paginatedRoles.data
      pagination.updateFromResponse(paginatedRoles)
    } else if (response.success && response.data?.success && response.data.data?.total == 0) {
      roles.value = []
      pagination.updateFromResponse({ data: [], limit: 0, page: 1, total: 0 })
    } else {
      error.value = response.error || 'Failed to load roles'
    }
  } catch (err) {
    error.value = 'Failed to load roles'
    console.error('Load roles error:', err)
  } finally {
    loading.value = false
  }
}

const addRole = () => {
  selectedRole.value = null
  showModal.value = true
}

const editRole = (role: Role) => {
  selectedRole.value = role
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  selectedRole.value = null
}

const deleteRole = async (role: Role) => {
  if (!confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
    return
  }

  try {
    const response = await RoleService.deleteRole(role.id)
    if (response.success) {
      await loadRoles()
    } else {
      error.value = response.error || 'Failed to delete role'
    }
  } catch (err) {
    error.value = 'Failed to delete role'
    console.error('Delete role error:', err)
  }
}

const handleSuccess = () => {
  closeModal()
  loadRoles()
}

// Load roles on component mount
onMounted(() => {
  loadRoles()
})
</script>
