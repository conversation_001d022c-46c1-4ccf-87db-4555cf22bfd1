<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Customer Documents</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Document
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 rounded-lg shadow">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search documents..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div class="flex gap-2">
          <select
            v-model="statusFilter"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Status</option>
            <option value="0">Inactive</option>
            <option value="1">Active</option>
          </select>
          <select
            v-model="docTypeFilter"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Types</option>
            <option value="1">Contract</option>
            <option value="2">Invoice</option>
            <option value="3">Certificate</option>
            <option value="4">Other</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading documents</h3>
          <div class="mt-2 text-sm text-red-700">{{ error }}</div>
        </div>
      </div>
    </div>

    <!-- Documents Table -->
    <div v-else class="bg-white shadow rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Document Name</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="doc in filteredDocuments" :key="doc.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ getCustomerName(doc.customerId) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ doc.name }}</div>
                <div class="text-sm text-gray-500">{{ doc.remark || 'No description' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getDocTypeClass(doc.docType)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getDocTypeText(doc.docType) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(doc.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getStatusText(doc.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(doc.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    @click="viewDocument(doc)"
                    class="text-blue-600 hover:text-blue-900 transition-colors"
                    title="View"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    @click="editDocument(doc)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors"
                    title="Edit"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteDocument(doc)"
                    class="text-red-600 hover:text-red-900 transition-colors hidden"
                    title="Delete"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add Document Modal -->
    <AddCustomerDocumentModal
      v-if="showAddModal"
      @close="showAddModal = false"
      @success="handleDocumentCreated"
    />

    <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg">
        <h3 class="text-lg font-medium mb-4">Edit Document Modal</h3>
        <p class="text-gray-600 mb-4">Modal implementation coming soon...</p>
        <button @click="showEditModal = false" class="px-4 py-2 bg-gray-300 rounded-md">Close</button>
      </div>
    </div>

    <div v-if="showViewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg">
        <h3 class="text-lg font-medium mb-4">View Document Modal</h3>
        <p class="text-gray-600 mb-4">Modal implementation coming soon...</p>
        <button @click="showViewModal = false" class="px-4 py-2 bg-gray-300 rounded-md">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { CustomerDocumentService, CustomerService } from '@/services/api'
import type { CustomerDocument, Customer } from '@/types'
import AddCustomerDocumentModal from '@/components/modals/AddCustomerDocumentModal.vue'

// Reactive data
const documents = ref<CustomerDocument[]>([])
const customers = ref<Customer[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const searchQuery = ref('')
const statusFilter = ref('')
const docTypeFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const selectedDocument = ref<CustomerDocument | null>(null)

// Computed properties
const filteredDocuments = computed(() => {
  let filtered = documents.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(doc => 
      doc.name.toLowerCase().includes(query) ||
      doc.remark?.toLowerCase().includes(query) ||
      getCustomerName(doc.customerId).toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(doc => doc.status.toString() === statusFilter.value)
  }

  if (docTypeFilter.value) {
    filtered = filtered.filter(doc => doc.docType.toString() === docTypeFilter.value)
  }

  return filtered
})

// Helper functions
const getCustomerName = (customerId: string): string => {
  const customer = customers.value.find(c => c.id === customerId)
  return customer?.name || 'Unknown Customer'
}

const getStatusText = (status: number): string => {
  return status === 1 ? 'Active' : 'Inactive'
}

const getStatusClass = (status: number): string => {
  return status === 1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
}

const getDocTypeText = (docType: number): string => {
  switch (docType) {
    case 1: return 'Contract'
    case 2: return 'Invoice'
    case 3: return 'Certificate'
    case 4: return 'Other'
    default: return 'Unknown'
  }
}

const getDocTypeClass = (docType: number): string => {
  switch (docType) {
    case 1: return 'bg-blue-100 text-blue-800'
    case 2: return 'bg-yellow-100 text-yellow-800'
    case 3: return 'bg-green-100 text-green-800'
    case 4: return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return dateString
  }
}

// Actions
const viewDocument = (doc: CustomerDocument) => {
  selectedDocument.value = doc
  showViewModal.value = true
}

const editDocument = (doc: CustomerDocument) => {
  selectedDocument.value = doc
  showEditModal.value = true
}

const deleteDocument = async (doc: CustomerDocument) => {
  if (confirm(`Are you sure you want to delete "${doc.name}"?`)) {
    try {
      const response = await CustomerDocumentService.deleteCustomerDocument(doc.id)
      if (response.success) {
        await loadData()
      } else {
        error.value = response.error || 'Failed to delete document'
      }
    } catch (err) {
      error.value = 'Failed to delete document'
      console.error('Delete document error:', err)
    }
  }
}

const handleDocumentCreated = async () => {
  await loadData()
}

const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // Load customers and documents in parallel
    const [customersResponse, documentsResponse] = await Promise.all([
      CustomerService.getCustomers({ limit: 100, page: 1 }),
      CustomerDocumentService.getCustomerDocuments({ limit: 100, page: 1 })
    ])

    if (customersResponse.success && customersResponse.data?.success && customersResponse.data.data) {
      const paginatedCustomers = customersResponse.data.data
      customers.value = paginatedCustomers.data
    }

    if (documentsResponse.success && documentsResponse.data?.success && documentsResponse.data.data) {
      const paginatedDocuments = documentsResponse.data.data
      documents.value = paginatedDocuments.data
    }
  } catch (err) {
    error.value = 'Failed to load data'
    console.error('Load data error:', err)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>
