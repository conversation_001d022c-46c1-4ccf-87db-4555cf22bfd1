<template>
  <div class="space-y-6">
    <!-- Header with Customer Info -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button
            @click="$router.go(-1)"
            class="text-gray-400 hover:text-gray-600 transition-colors"
            title="Go Back"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div class="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center">
            <span class="text-lg font-medium text-white">
              {{ customer?.name?.charAt(0)?.toUpperCase() || '?' }}
            </span>
          </div>
          <div>
            <h1 class="text-lg font-bold text-gray-900">{{ customer?.name || 'Loading...' }}</h1>
            <p class="text-sm text-gray-500">Customer Details</p>
          </div>
        </div>
      </div>
    </div>
    

    <!-- Tabs -->
    <div class="bg-white rounded-lg shadow">
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6">
          <button
            @click="activeTab = 'packages'"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm transition-colors focus:outline-none focus:ring-0',
              activeTab === 'packages'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            Packages
          </button>
          <button
            @click="activeTab = 'equipment'"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm transition-colors focus:outline-none focus:ring-0',
              activeTab === 'equipment'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            Equipment
          </button>
          <button
            @click="activeTab = 'documents'"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm transition-colors focus:outline-none focus:ring-0',
              activeTab === 'documents'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            Documents
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="p-6">
        <!-- Packages Tab -->
        <div v-if="activeTab === 'packages'" class="space-y-4">
          <div class="flex justify-between items-center">
            <h3 class="text-base font-medium text-gray-900">Customer Packages</h3>
            <div class="flex space-x-2">
              <button
                @click="showAddPackageModal = true"
                class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium text-sm"
              >
                Add Package
              </button>
            </div>
          </div>

          <div v-if="packages && packages.length === 0" class="text-center py-8 text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
            </svg>
            <p class="mt-2">No packages found</p>
            <p class="text-sm text-gray-400">Get started by adding a package for this customer</p>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Package Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Circuit ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service No</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Speed</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Provider</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="pkg in packages" :key="pkg.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ getPackageName(pkg.packageId) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ pkg.circuitId || 'N/A' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ pkg.servicesNo || 'N/A' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ pkg.speed || 'N/A' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ pkg.provider || 'N/A' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getPackageStatusClass(pkg.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                      {{ getPackageStatusText(pkg.status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-1">
                      <button
                        @click="editPackage(pkg)"
                        class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded hover:bg-indigo-50"
                        title="Edit Package"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Equipment Tab -->
        <div v-if="activeTab === 'equipment'" class="space-y-4">
          <div class="flex justify-between items-center">
            <h3 class="text-base font-medium text-gray-900">Customer Equipment</h3>
            <div class="flex space-x-2">
              <button
                @click="showAddEquipmentModal = true"
                class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium text-sm"
              >
                Add Equipment
              </button>
            </div>
          </div>

          <div v-if="equipment && equipment.length === 0" class="text-center py-8 text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 3.75H6.912a2.25 2.25 0 00-2.15 1.588L2.35 13.177a2.25 2.25 0 00-.1.661V18a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18v-4.162c0-.224-.034-.447-.1-.661L19.24 5.338a2.25 2.25 0 00-2.15-1.588H15M2.25 13.5h3.86a2.25 2.25 0 012.012 1.244l.256.512a2.25 2.25 0 002.013 1.244h3.218a2.25 2.25 0 002.013-1.244l.256-.512a2.25 2.25 0 012.013-1.244h3.859M12 3v8.25m0 0l-3-3m3 3l3-3" />
            </svg>
            <p class="mt-2">No equipment found</p>
            <p class="text-sm text-gray-400">Get started by adding equipment for this customer</p>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Brand</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock In Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Warranty Exp</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="eq in equipment" :key="eq.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ getEquipmentType(eq.equipmentId) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ getEquipmentBrand(eq.equipmentId) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ getEquipmentModel(eq.equipmentId) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ eq.sn || 'N/A' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ eq.stockinDate ? formatDate(eq.stockinDate) : 'N/A' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ eq.warrantyExpDate ? formatDate(eq.warrantyExpDate) : 'N/A' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-1">
                      <button
                        @click="viewEquipment(eq)"
                        class="text-blue-600 hover:text-blue-900 transition-colors p-1 rounded hover:bg-blue-50 hidden"
                        title="View Equipment"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      <button
                        @click="editEquipment(eq)"
                        class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded hover:bg-indigo-50"
                        title="Edit Equipment"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Documents Tab -->
        <div v-if="activeTab === 'documents'" class="space-y-4">
          <div class="flex justify-between items-center">
            <h3 class="text-base font-medium text-gray-900">Customer Documents</h3>
            <button
              @click="showAddDocumentModal = true"
              class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium text-sm"
            >
              Add Document
            </button>
          </div>

          <div v-if="documents && documents.length === 0" class="text-center py-8 text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
            </svg>
            <p class="mt-2">No documents found</p>
            <p class="text-sm text-gray-400">Get started by adding a document for this customer</p>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Original Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="doc in documents" :key="doc.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ doc.originalName || 'N/A' }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ formatFileSize(doc.size) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ formatDate(doc.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-1">
                      <button
                        @click="downloadFile(doc.id, doc.originalName)"
                        class="text-green-600 hover:text-green-900 transition-colors p-1 rounded hover:bg-green-50"
                        title="Download File"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                      </button>
                      <button
                        @click="editDocument(doc)"
                        class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded hover:bg-indigo-50"
                        title="Edit Document"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Package Modal -->
    <CustomerPackageModal
      v-if="showAddPackageModal || showEditPackageModal"
      :package="showEditPackageModal && selectedPackage ? selectedPackage : undefined"
      :customer-id="customerId"
      @close="closePackageModals"
      @success="handlePackageSuccess"
    />

    <!-- Add/Edit Document Modal -->
    <AddDocumentModal
      v-if="showAddDocumentModal || showEditDocumentModal"
      :customer-id="customerId"
      :document="showEditDocumentModal ? selectedDocument : undefined"
      @close="closeDocumentModals"
      @success="handleDocumentSuccess"
    />

    <!-- Add/Edit Equipment Modal -->
    <CustomerEquipmentModal
      v-if="showAddEquipmentModal || showEditEquipmentModal"
      :customer-id="customerId"
      :equipment="showEditEquipmentModal && selectedEquipment ? selectedEquipment : undefined"
      @close="closeEquipmentModals"
      @success="handleEquipmentSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { CustomerService, CustomerPackageService, CustomerDocumentService, EquipmentInventoryService, PackageService, EquipmentService, apiClient } from '@/services/api'
import type { Customer, CustomerPackage, CustomerDocument, EquipmentInventory, Package, Equipment } from '@/types'
import CustomerPackageModal from '@/components/modals/CustomerPackageModal.vue'
import AddDocumentModal from '@/components/modals/AddDocumentModal.vue'
import CustomerEquipmentModal from '@/components/modals/CustomerEquipmentModal.vue'

const route = useRoute()
const customerId = route.params.id as string

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://cmsapi.jenexusenergy.com/api/v1'

// Reactive data
const customer = ref<Customer | null>(null)
const packages = ref<CustomerPackage[]>([])
const documents = ref<CustomerDocument[]>([])
const equipment = ref<EquipmentInventory[]>([])
const packageNames = ref<Record<string, string>>({})
const equipmentDetails = ref<Record<string, Equipment>>({})
const loading = ref(false)
const activeTab = ref('packages')

// Modal states
const showAddPackageModal = ref(false)
const showAddDocumentModal = ref(false)
const showAddEquipmentModal = ref(false)
const showEditPackageModal = ref(false)
const showEditDocumentModal = ref(false)
const showViewEquipmentModal = ref(false)
const showEditEquipmentModal = ref(false)
const selectedPackage = ref<CustomerPackage | null>(null)
const selectedDocument = ref<CustomerDocument | null>(null)
const selectedEquipment = ref<EquipmentInventory | null>(null)

// Load customer data
const loadCustomer = async () => {
  try {
    const response = await CustomerService.getCustomerById(customerId)
    if (response.success && response.data?.success && response.data.data) {
      customer.value = response.data.data
    }
  } catch (error) {
    console.error('Failed to load customer:', error)
  }
}

// Load customer packages
const loadPackages = async () => {
  try {
    const response = await CustomerPackageService.getCustomerPackagesByCustomerId(customerId, {
      limit: 100,
      page: 1
    })
    if (response.success && response.data?.success && response.data.data) {
      const paginatedData = response.data.data
      packages.value = (paginatedData as unknown as CustomerPackage[]) || []
      
      // Load package names for each customer package
      await loadPackageNames()
    }
  } catch (error) {
    console.error('Failed to load packages:', error)
  }
}

// Load package names by packageId
const loadPackageNames = async () => {
  const packageIds = [...new Set(packages.value.map(pkg => pkg.packageId).filter(Boolean))]
  
  for (const packageId of packageIds) {
    try {
      const response = await PackageService.getPackageById(packageId)
      if (response.success && response.data?.success && response.data.data) {
        packageNames.value[packageId] = response.data.data.name
      }
    } catch (error) {
      console.error(`Failed to load package name for ${packageId}:`, error)
    }
  }
}

// Get package name by packageId
const getPackageName = (packageId: string): string => {
  return packageNames.value[packageId] || 'N/A'
}

// Load customer documents
const loadDocuments = async () => {
  try {
    const response = await CustomerDocumentService.getCustomerDocumentsByCustomerId(customerId, {
      limit: 100,
      page: 1
    })
    if (response.success && response.data?.success && response.data.data) {
      const paginatedData = response.data.data
      documents.value = paginatedData.data
    }
  } catch (error) {
    console.error('Failed to load documents:', error)
  }
}

// Load customer equipment
const loadEquipment = async () => {
  try {
    const response = await EquipmentInventoryService.getEquipmentInventoryByCustomerId(customerId, {
      limit: 100,
      page: 1
    })
    if (response.success && response.data?.success && response.data.data) {
      const paginatedData = response.data.data
      equipment.value = paginatedData.data || []
      
      // Load equipment details for each equipment inventory
      await loadEquipmentDetails()
    }
  } catch (error) {
    console.error('Failed to load equipment:', error)
  }
}

// Load equipment details by equipmentId
const loadEquipmentDetails = async () => {
  const equipmentIds = [...new Set(equipment.value.map(eq => eq.equipmentId).filter(Boolean))]
  
  for (const equipmentId of equipmentIds) {
    try {
      const response = await EquipmentService.getEquipmentById(equipmentId)
      if (response.success && response.data?.success && response.data.data) {
        equipmentDetails.value[equipmentId] = response.data.data
      }
    } catch (error) {
      console.error(`Failed to load equipment details for ${equipmentId}:`, error)
    }
  }
}

// Get equipment type (category0)
const getEquipmentType = (equipmentId: string): string => {
  return equipmentDetails.value[equipmentId]?.category0 || 'N/A'
}

// Get equipment brand (category1)
const getEquipmentBrand = (equipmentId: string): string => {
  return equipmentDetails.value[equipmentId]?.category1 || 'N/A'
}

// Get equipment model (category2)
const getEquipmentModel = (equipmentId: string): string => {
  return equipmentDetails.value[equipmentId]?.category2 || 'N/A'
}

// Package status helpers
const getPackageStatusClass = (status: number): string => {
  switch (status) {
    case 1: return 'bg-green-100 text-green-800'
    case 0: return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getPackageStatusText = (status: number): string => {
  switch (status) {
    case 1: return 'Active'
    case 0: return 'Inactive'
    default: return 'Unknown'
  }
}

// Document type helpers
const getDocTypeClass = (docType: number): string => {
  switch (docType) {
    case 1: return 'bg-blue-100 text-blue-800'
    case 2: return 'bg-yellow-100 text-yellow-800'
    case 3: return 'bg-green-100 text-green-800'
    case 4: return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getDocTypeText = (docType: number): string => {
  switch (docType) {
    case 1: return 'Contract'
    case 2: return 'Invoice'
    case 3: return 'Report'
    case 4: return 'Other'
    default: return 'Unknown'
  }
}

// Document status helpers
const getDocumentStatusClass = (status: number): string => {
  switch (status) {
    case 1: return 'bg-green-100 text-green-800'
    case 0: return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getDocumentStatusText = (status: number): string => {
  switch (status) {
    case 1: return 'Active'
    case 0: return 'Inactive'
    default: return 'Unknown'
  }
}

// Equipment status helpers
const getEquipmentStatusClass = (status: number): string => {
  switch (status) {
    case 1: return 'bg-green-100 text-green-800'
    case 0: return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getEquipmentStatusText = (status: number): string => {
  switch (status) {
    case 1: return 'Active'
    case 0: return 'Inactive'
    default: return 'Unknown'
  }
}

// Date formatter
const formatDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return dateString
  }
}

// File size formatter (convert bytes to KB)
const formatFileSize = (bytes?: number): string => {
  if (!bytes || bytes === 0) return '0 KB'
  const kb = bytes / 1024
  return `${kb.toFixed(2)} KB`
}

// Download file with authorization
const downloadFile = async (fileId: string, fileName?: string) => {
  try {
    const response = await apiClient['axiosInstance'].get(`/filedownload/${fileId}`, {
      responseType: 'blob'
    })

    // Create a blob URL and trigger download
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to download file:', error)
    alert('Failed to download file. Please try again.')
  }
}

// Package actions
const editPackage = (pkg: CustomerPackage) => {
  selectedPackage.value = pkg
  showEditPackageModal.value = true
}

// Document actions
const editDocument = (doc: CustomerDocument) => {
  selectedDocument.value = doc
  showEditDocumentModal.value = true
}

const closeDocumentModals = () => {
  showAddDocumentModal.value = false
  showEditDocumentModal.value = false
  selectedDocument.value = null
}

// Equipment actions
const viewEquipment = (eq: EquipmentInventory) => {
  selectedEquipment.value = eq
  showViewEquipmentModal.value = true
}

const editEquipment = (eq: EquipmentInventory) => {
  selectedEquipment.value = eq
  showEditEquipmentModal.value = true
}

// Success handlers
const handlePackageSuccess = () => {
  loadPackages()
  showAddPackageModal.value = false
  showEditPackageModal.value = false
}

const closePackageModals = () => {
  showAddPackageModal.value = false
  showEditPackageModal.value = false
  selectedPackage.value = null
}

const handleDocumentSuccess = () => {
  loadDocuments()
  closeDocumentModals()
}

const handleEquipmentSuccess = () => {
  loadEquipment()
  closeEquipmentModals()
}

const closeEquipmentModals = () => {
  showAddEquipmentModal.value = false
  showEditEquipmentModal.value = false
  selectedEquipment.value = null
}

// Load all data
const loadData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadCustomer(),
      loadPackages(),
      loadDocuments(),
      loadEquipment()
    ])
  } finally {
    loading.value = false
  }
}

// Initialize
onMounted(() => {
  loadData()
})
</script>
