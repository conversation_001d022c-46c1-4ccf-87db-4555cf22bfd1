<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Customers</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Customer
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 md:p-6 lg:p-8 rounded-lg shadow">
      <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search customers..."
            @keyup.enter="handleSearch"
            class="w-full px-3 md:px-4 lg:px-5 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <button
          @click="clearSearch"
          class="px-4 md:px-6 lg:px-8 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
        >
          Clear
        </button>
      </div>
    </div>

    <!-- Customers Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-900">
          All Customers ({{ pagination.state.total }})
        </h3>
      </div>

      <div v-if="customers.length === 0" class="p-6 text-center text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
        </svg>
        <p class="mt-2">No customers found</p>
        <p class="text-sm text-gray-400">Get started by adding your first customer</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact Person 1
              </th>
              <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact Person 2
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="(customer, index) in customers"
              :key="index"
              class="hover:bg-gray-50 transition-colors"
            >
              <!-- Customer Name -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                    <span class="text-xs sm:text-sm font-medium text-white">
                      {{ customer.name?.charAt(0)?.toUpperCase() || '?' }}
                    </span>
                  </div>
                  <div class="ml-4">
                    <div class="text-xs sm:text-sm font-medium text-gray-900">{{ customer.name || 'Unknown Customer' }}</div>
                    <div class="text-xs sm:text-sm text-gray-500">{{ customer.fullName || '-' }}</div>
                  </div>
                </div>
              </td>

              <!-- Contact Person 1 -->
              <td class="hidden sm:table-cell px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ customer.contactPerson || 'No contact person' }}</div>
                <div class="text-sm text-gray-900">{{ customer.contactNo || 'No phone' }}</div>
                <div class="text-sm text-gray-500">{{ customer.email || 'No email' }}</div>
              </td>

              <!-- Contact Person 2 -->
              <td class="hidden sm:table-cell px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ customer.contactPerson2 || 'No contact person' }}</div>
                <div class="text-sm text-gray-900">{{ customer.contactNo2 || 'No phone' }}</div>
                <div class="text-sm text-gray-500">{{ customer.email2 || 'No email' }}</div>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-1">
                  <button
                    @click="editCustomer(customer)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded hover:bg-indigo-50"
                    title="Edit Customer"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="viewCustomerPackages(customer)"
                    class="text-green-600 hover:text-green-900 transition-colors p-1 rounded hover:bg-green-50"
                    title="View Packages & Documents"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                      <path stroke-linecap="round" stroke-linejoin="round" d="m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                    </svg>
                  </button>  
                  <button
                    @click="deleteCustomer(customer)"
                    class="text-red-600 hover:text-red-900 transition-colors p-1 rounded hover:bg-red-50 hidden"
                    title="Delete Customer"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <Pagination
        v-if="customers.length > 0"
        :current-page="pagination.state.currentPage"
        :page-size="pagination.state.pageSize"
        :total="pagination.state.total"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- Customer Modal (Add/Edit) -->
    <CustomerModal
      v-if="showAddModal"
      @close="showAddModal = false"
      @success="handleCustomerSuccess"
    />
    <CustomerModal
      v-if="showEditModal && selectedCustomer"
      :customer="selectedCustomer"
      @close="showEditModal = false"
      @success="handleCustomerSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { CustomerService } from '@/services/api'
import type { Customer } from '@/types'
import CustomerModal from '@/components/modals/CustomerModal.vue'
import Pagination from '@/components/Pagination.vue'
import { usePagination } from '@/composables/usePagination'

const router = useRouter()

// Reactive data
const customers = ref<Customer[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const searchQuery = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const selectedCustomer = ref<Customer | null>(null)

// Pagination setup
const pagination = usePagination({
  initialPageSize: 10,
  onPageChange: async () => {
    await loadCustomers()
  }
})

// Watch for search query changes with debounce
let searchTimeout: number | null = null
watch(searchQuery, async () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(async () => {
    pagination.reset()
    await loadCustomers()
  }, 300)
})

const handleSearch = async () => {
  pagination.reset()
  await loadCustomers()
}

const clearSearch = async () => {
  searchQuery.value = ''
  pagination.reset()
  await loadCustomers()
}

// Pagination handlers
const handlePageChange = async (page: number) => {
  await pagination.goToPage(page)
}

const handlePageSizeChange = async (pageSize: number) => {
  await pagination.changePageSize(pageSize)
}

const handleCustomerSuccess = () => {
  // Reload customers after successful add/edit
  loadCustomers()
}

const viewCustomerPackages = (customer: Customer) => {
  router.push(`/customers/${customer.id}`)
}

const editCustomer = (customer: Customer) => {
  selectedCustomer.value = customer
  showEditModal.value = true
}

const deleteCustomer = async (customer: Customer) => {
  if (confirm(`Are you sure you want to delete ${customer.name || 'this customer'}?`)) {
    try {
      const response = await CustomerService.deleteCustomer(customer.id)
      if (response.success && response.data && response.data.success) {
        // Remove from local array
        customers.value = customers.value.filter(c => c.id !== customer.id)
      } else {
        alert('Failed to delete customer: ' + (response.error || 'Unknown error'))
      }
    } catch (err) {
      alert('Failed to delete customer: ' + (err instanceof Error ? err.message : 'Unknown error'))
    }
  }
}

const loadCustomers = async () => {
  loading.value = true
  error.value = null

  try {
    const params = pagination.getQueryParamsWithSearch(searchQuery.value)
    const response = await CustomerService.getCustomers(params)

    if (response.success && response.data?.success && response.data.data) {
      const paginatedData = response.data.data
      customers.value = paginatedData.data
      pagination.updateFromResponse(paginatedData)
    } else {
      error.value = response.error || 'Failed to load customers'
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load customers'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadCustomers()
})
</script>
