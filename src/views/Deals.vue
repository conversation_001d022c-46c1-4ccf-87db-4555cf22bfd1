<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Deals</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Deal
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 md:p-6 lg:p-8 rounded-lg shadow">
      <div class="flex flex-col lg:flex-row items-stretch lg:items-center gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search deals..."
            class="w-full px-3 md:px-4 lg:px-5 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div class="flex flex-col sm:flex-row gap-4">
          <select
            v-model="statusFilter"
            class="px-3 md:px-4 lg:px-5 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Status</option>
            <option value="draft">Draft</option>
            <option value="pending_presales">Pending Presales</option>
            <option value="presales_review">Presales Review</option>
            <option value="technical_review">Technical Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="on_hold">On Hold</option>
            <option value="closed">Closed</option>
            <option value="cancelled">Cancelled</option>
          </select>
          <select
            v-model="stageFilter"
            class="px-3 md:px-4 lg:px-5 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Stages</option>
            <option value="initial">Initial</option>
            <option value="site_validation">Site Validation</option>
            <option value="technical_proposal">Technical Proposal</option>
            <option value="feasibility_check">Feasibility Check</option>
            <option value="hardware_procurement">Hardware Procurement</option>
            <option value="vendor_quotes">Vendor Quotes</option>
            <option value="pnl_review">P&L Review</option>
            <option value="final_approval">Final Approval</option>
            <option value="implementation">Implementation</option>
          </select>
          <select
            v-model="priorityFilter"
            class="px-3 md:px-4 lg:px-5 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Priorities</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
          <button
            @click="clearFilters"
            class="px-4 md:px-6 lg:px-8 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Clear
          </button>
        </div>
      </div>
    </div>

    <!-- Deals Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-900">
          All Deals ({{ pagination.state.total }})
        </h3>
      </div>

      <div v-if="deals.length === 0" class="p-6 text-center text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="mt-2">No deals found</p>
        <p class="text-sm text-gray-400">Get started by adding your first deal</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deal</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stage</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales Executive</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expected Close</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="deal in deals" :key="deal.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ deal.dealNo }}</div>
                  <div class="text-sm text-gray-500">{{ deal.title }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getCustomerName(deal.customerId) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ formatCurrency(deal.totalValue, deal.currency) }}</div>
                <div class="text-sm text-gray-500">Margin: {{ deal.expectedMargin }}%</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(deal.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ formatStatus(deal.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStageBadgeClass(deal.stage)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ formatStage(deal.stage) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getPriorityBadgeClass(deal.priority)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ formatPriority(deal.priority) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getSalesExecutiveName(deal.salesExecutive) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ formatDate(deal.expectedCloseDate) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  @click="editDeal(deal)"
                  class="text-blue-600 hover:text-blue-900 transition-colors"
                >
                  Edit
                </button>
                <button
                  @click="approveDeal(deal)"
                  v-if="deal.status === 'pending_presales' || deal.status === 'presales_review'"
                  class="text-green-600 hover:text-green-900 transition-colors"
                >
                  Approve
                </button>
                <button
                  @click="closeDeal(deal)"
                  v-if="deal.status === 'approved'"
                  class="text-purple-600 hover:text-purple-900 transition-colors"
                >
                  Close
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="px-6 py-4 border-t border-gray-200">
        <Pagination
          :current-page="pagination.state.currentPage"
          :total-pages="Math.ceil(pagination.state.total / pagination.state.pageSize)"
          :page-size="pagination.state.pageSize"
          :total="pagination.state.total"
          @page-change="pagination.goToPage"
          @page-size-change="pagination.changePageSize"
        />
      </div>
    </div>

    <!-- Deal Modal -->
    <DealModal
      v-if="showAddModal || showEditModal"
      :deal="selectedDeal"
      @close="closeModal"
      @success="handleDealSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { DealService } from '@/services/api'
import type { Deal } from '@/types'
import DealModal from '@/components/modals/DealModal.vue'
import Pagination from '@/components/Pagination.vue'
import { usePagination } from '@/composables/usePagination'

// Reactive data
const deals = ref<Deal[]>([])
const searchQuery = ref('')
const statusFilter = ref('')
const stageFilter = ref('')
const priorityFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const selectedDeal = ref<Deal | undefined>(undefined)

// Pagination
const pagination = usePagination()

// Debounced search
let searchTimeout: ReturnType<typeof setTimeout> | null = null
watch(searchQuery, () => {
  if (searchTimeout) clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    fetchDeals()
  }, 300)
})

// Watch filters
watch([statusFilter, stageFilter, priorityFilter], () => {
  fetchDeals()
})

// Methods
const fetchDeals = async () => {
  try {
    const params = pagination.getQueryParamsWithSearch(searchQuery.value)
    
    // Add filters to params
    const queryParams: any = { ...params }
    if (statusFilter.value) {
      queryParams.status = statusFilter.value
    }
    if (stageFilter.value) {
      queryParams.stage = stageFilter.value
    }
    if (priorityFilter.value) {
      queryParams.priority = priorityFilter.value
    }

    const response = await DealService.getDeals(queryParams)
    
    if (response.success && response.data && response.data.success && response.data.data) {
      deals.value = response.data.data.data || []
      pagination.updateFromResponse(response.data.data)
    } else {
      console.error('Failed to fetch deals:', response.error)
      deals.value = []
    }
  } catch (error) {
    console.error('Error fetching deals:', error)
    deals.value = []
  }
}

const clearFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  stageFilter.value = ''
  priorityFilter.value = ''
  pagination.reset()
  fetchDeals()
}

const editDeal = (deal: Deal) => {
  selectedDeal.value = deal
  showEditModal.value = true
}

const approveDeal = async (deal: Deal) => {
  if (confirm(`Are you sure you want to approve deal ${deal.dealNo}?`)) {
    try {
      const response = await DealService.approveDeal(deal.id)
      if (response.success && response.data && response.data.success) {
        await fetchDeals()
        alert('Deal approved successfully!')
      } else {
        alert('Failed to approve deal: ' + (response.error || 'Unknown error'))
      }
    } catch (error) {
      alert('Failed to approve deal: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }
}

const closeDeal = async (deal: Deal) => {
  if (confirm(`Are you sure you want to close deal ${deal.dealNo}?`)) {
    try {
      const response = await DealService.closeDeal(deal.id)
      if (response.success && response.data && response.data.success) {
        await fetchDeals()
        alert('Deal closed successfully!')
      } else {
        alert('Failed to close deal: ' + (response.error || 'Unknown error'))
      }
    } catch (error) {
      alert('Failed to close deal: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  selectedDeal.value = undefined
}

const handleDealSuccess = () => {
  fetchDeals()
  closeModal()
}

// Utility functions
const getCustomerName = (customerId: string) => {
  // TODO: Implement customer lookup
  return `Customer ${customerId.slice(-6)}`
}

const getSalesExecutiveName = (salesExecutiveId: string) => {
  // TODO: Implement user lookup
  return `Sales ${salesExecutiveId.slice(-6)}`
}

const formatCurrency = (amount: number, currency: string) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD'
  }).format(amount)
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString()
}

const formatStatus = (status: string) => {
  return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatStage = (stage: string) => {
  return stage.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatPriority = (priority: string) => {
  return priority.charAt(0).toUpperCase() + priority.slice(1)
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    draft: 'bg-gray-100 text-gray-800',
    pending_presales: 'bg-yellow-100 text-yellow-800',
    presales_review: 'bg-blue-100 text-blue-800',
    technical_review: 'bg-purple-100 text-purple-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    on_hold: 'bg-orange-100 text-orange-800',
    closed: 'bg-indigo-100 text-indigo-800',
    cancelled: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStageBadgeClass = (stage: string) => {
  const classes = {
    initial: 'bg-gray-100 text-gray-800',
    site_validation: 'bg-blue-100 text-blue-800',
    technical_proposal: 'bg-purple-100 text-purple-800',
    feasibility_check: 'bg-yellow-100 text-yellow-800',
    hardware_procurement: 'bg-orange-100 text-orange-800',
    vendor_quotes: 'bg-pink-100 text-pink-800',
    pnl_review: 'bg-indigo-100 text-indigo-800',
    final_approval: 'bg-green-100 text-green-800',
    implementation: 'bg-teal-100 text-teal-800'
  }
  return classes[stage as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getPriorityBadgeClass = (priority: string) => {
  const classes = {
    low: 'bg-green-100 text-green-800',
    medium: 'bg-yellow-100 text-yellow-800',
    high: 'bg-orange-100 text-orange-800',
    urgent: 'bg-red-100 text-red-800'
  }
  return classes[priority as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

// Lifecycle
onMounted(() => {
  fetchDeals()
})
</script>
