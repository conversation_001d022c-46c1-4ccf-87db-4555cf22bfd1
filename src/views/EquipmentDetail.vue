<template>
  <div class="space-y-6">
    <!-- Header with Equipment Info -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button
            @click="$router.go(-1)"
            class="text-gray-400 hover:text-gray-600 transition-colors"
            title="Go Back"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div class="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <div>
            <h1 class="text-lg font-bold text-gray-900">{{ equipment?.name }}</h1>
            <p class="text-sm text-gray-500">{{ equipment?.remark }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Equipment Info Summary -->
    <div v-if="equipment" class="bg-white rounded-lg shadow p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Equipment Information</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-xs font-medium text-gray-500 mb-1">Category</label>
          <p class="text-sm text-gray-900">{{ equipment.category0 }} • {{ equipment.category1 }} • {{ equipment.category2 }}</p>
        </div>
        <div>
          <label class="block text-xs font-medium text-gray-500 mb-1">Serial Number</label>
          <p class="text-sm text-gray-900 font-mono">{{ equipment.sn || 'No S/N' }}</p>
        </div>
        <div>
          <label class="block text-xs font-medium text-gray-500 mb-1">Status</label>
          <span
            class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
            :class="getStatusClass(equipment.status)"
          >
            {{ getStatusText(equipment.status) }}
          </span>
        </div>
      </div>
    </div>

    <!-- Inventories Section -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h3 class="text-base font-medium text-gray-900">Equipment Inventories</h3>
          <button
            @click="openAddInventoryModal"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium text-sm"
          >
            Add Inventory
          </button>
        </div>
      </div>

      <div class="p-6">
        <div v-if="inventories && inventories.length === 0" class="text-center py-8 text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
          </svg>
          <p class="mt-2">No inventories found</p>
          <p class="text-sm text-gray-400">Get started by adding an inventory item for this equipment</p>
        </div>

        <div v-else class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock In</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock In By</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Out</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Out By</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Warranty</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="inventory in inventories" :key="inventory.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ getCustomerName(inventory.customerId) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">{{ inventory.sn || 'N/A' }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                    :class="getInventoryStatusClass(inventory.status)"
                  >
                    {{ getInventoryStatusText(inventory.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ inventory.stockinDate ? formatDate(inventory.stockinDate) : 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ getUserName(inventory.stockinBy) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ inventory.stockoutDate ? formatDate(inventory.stockoutDate) : 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ getUserName(inventory.stockoutBy) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ inventory.warrantyExpDate ? formatDate(inventory.warrantyExpDate) : 'No warranty' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-1">
                    <button
                      @click="editInventory(inventory)"
                      class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded hover:bg-indigo-50"
                      title="Edit Inventory"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Inventory Modal -->
    <InventoryModal
      v-if="showInventoryModal"
      :inventory="selectedInventory"
      :equipmentId="equipmentId"
      @close="closeInventoryModal"
      @success="handleInventorySuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { EquipmentService, EquipmentInventoryService, CustomerService, UserService } from '@/services/api'
import type { Equipment, EquipmentInventory, Customer, UserResponse } from '@/types'
import InventoryModal from '@/components/modals/InventoryModal.vue'

const route = useRoute()
const equipmentId = route.params.id as string

// Reactive data
const equipment = ref<Equipment | null>(null)
const inventories = ref<EquipmentInventory[]>([])
const customerCache = ref<Map<string, Customer>>(new Map())
const userCache = ref<Map<string, UserResponse>>(new Map())
const loading = ref(false)

// Modal states
const showInventoryModal = ref(false)
const selectedInventory = ref<EquipmentInventory | undefined>(undefined)

// Load equipment data
const loadEquipment = async () => {
  try {
    const response = await EquipmentService.getEquipmentById(equipmentId)
    if (response.success && response.data?.success && response.data.data) {
      equipment.value = response.data.data
    }
  } catch (error) {
    console.error('Failed to load equipment:', error)
  }
}

// Load equipment inventories
const loadInventories = async () => {
  try {
    const response = await EquipmentInventoryService.getEquipmentInventoryByEquipmentId(equipmentId, {
      limit: 100,
      page: 1
    })
    if (response.success && response.data?.success && response.data.data) {
      const paginatedData = response.data.data
      inventories.value = paginatedData.data || []
    }
  } catch (error) {
    console.error('Failed to load inventories:', error)
  }
}

// Load customer by ID
const loadCustomerById = async (customerId: string): Promise<Customer | null> => {
  // Check cache first
  if (customerCache.value.has(customerId)) {
    return customerCache.value.get(customerId) || null
  }

  try {
    const response = await CustomerService.getCustomerById(customerId)
    if (response.success && response.data?.success && response.data.data) {
      const customer = response.data.data
      customerCache.value.set(customerId, customer)
      return customer
    }
  } catch (error) {
    console.error(`Failed to load customer ${customerId}:`, error)
  }
  return null
}

// Load user by ID
const loadUserById = async (userId: string): Promise<UserResponse | null> => {
  // Check cache first
  if (userCache.value.has(userId)) {
    return userCache.value.get(userId) || null
  }

  try {
    const response = await UserService.getUserById(userId)
    if (response.success && response.data?.success && response.data.data) {
      const user = response.data.data
      userCache.value.set(userId, user)
      return user
    }
  } catch (error) {
    console.error(`Failed to load user ${userId}:`, error)
  }
  return null
}

// Helper functions
const getCustomerName = (customerId: string): string => {
  const customer = customerCache.value.get(customerId)
  return customer?.name || `Customer ${customerId}`
}

const getUserName = (userId: string | undefined): string => {
  if (!userId) return 'N/A'
  const user = userCache.value.get(userId)
  return user?.name || userId
}

const getStatusText = (status: number) => {
  switch (status) {
    case 1: return 'Active'
    case 2: return 'Maintenance'
    case 3: return 'Expired'
    case 0: return 'Inactive'
    default: return 'Unknown'
  }
}

const getStatusClass = (status: number) => {
  switch (status) {
    case 1: return 'bg-green-100 text-green-800'
    case 2: return 'bg-yellow-100 text-yellow-800'
    case 3: return 'bg-red-100 text-red-800'
    case 0: return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getInventoryStatusText = (status: number) => {
  switch (status) {
    case 1: return 'Active'
    case 2: return 'Delivered'
    case 0: return 'Inactive'
    default: return 'Unknown'
  }
}

const getInventoryStatusClass = (status: number) => {
  switch (status) {
    case 1: return 'bg-green-100 text-green-800'
    case 0: return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return dateString
  }
}

// Inventory actions
const openAddInventoryModal = () => {
  selectedInventory.value = undefined
  showInventoryModal.value = true
}

const editInventory = (inventory: EquipmentInventory) => {
  selectedInventory.value = inventory
  showInventoryModal.value = true
}

const closeInventoryModal = () => {
  showInventoryModal.value = false
  selectedInventory.value = undefined
}

const handleInventorySuccess = async () => {
  await loadInventories()
  await loadRequiredUserAndCustomerData()
}

// Load required user and customer data based on inventories
const loadRequiredUserAndCustomerData = async () => {
  if (!inventories.value.length) return

  const customerIds = new Set<string>()
  const userIds = new Set<string>()

  // Collect unique customer and user IDs from inventories
  inventories.value.forEach(inventory => {
    if (inventory.customerId) {
      customerIds.add(inventory.customerId)
    }
    if (inventory.stockinBy) {
      userIds.add(inventory.stockinBy)
    }
    if (inventory.stockoutBy) {
      userIds.add(inventory.stockoutBy)
    }
  })

  // Load customers and users in parallel
  const promises: Promise<any>[] = []

  customerIds.forEach(customerId => {
    promises.push(loadCustomerById(customerId))
  })

  userIds.forEach(userId => {
    promises.push(loadUserById(userId))
  })

  await Promise.all(promises)
}

// Load all data
const loadData = async () => {
  loading.value = true
  try {
    // First load equipment and inventories
    await Promise.all([
      loadEquipment(),
      loadInventories()
    ])

    // Then load required user and customer data based on inventories
    await loadRequiredUserAndCustomerData()
  } finally {
    loading.value = false
  }
}

// Initialize
onMounted(() => {
  loadData()
})
</script>
