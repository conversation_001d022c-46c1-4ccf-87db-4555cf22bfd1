import { ref, computed, reactive, readonly } from 'vue'
import type { PaginationQuery, PaginatedResponse } from '@/types'

export interface PaginationState {
  currentPage: number
  pageSize: number
  total: number
  loading: boolean
}

export interface UsePaginationOptions {
  initialPageSize?: number
  onPageChange?: (page: number, pageSize: number) => Promise<void>
  onPageSizeChange?: (pageSize: number) => Promise<void>
}

export function usePagination(options: UsePaginationOptions = {}) {
  const {
    initialPageSize = 10,
    onPageChange,
    onPageSizeChange
  } = options

  // Reactive state
  const state = reactive<PaginationState>({
    currentPage: 1,
    pageSize: initialPageSize,
    total: 0,
    loading: false
  })

  // Computed properties
  const totalPages = computed(() => Math.ceil(state.total / state.pageSize))
  
  const hasNextPage = computed(() => state.currentPage < totalPages.value)
  
  const hasPrevPage = computed(() => state.currentPage > 1)

  const startItem = computed(() => {
    if (state.total === 0) return 0
    return (state.currentPage - 1) * state.pageSize + 1
  })

  const endItem = computed(() => {
    const end = state.currentPage * state.pageSize
    return Math.min(end, state.total)
  })

  // Methods
  const goToPage = async (page: number) => {
    if (page >= 1 && page <= totalPages.value && page !== state.currentPage) {
      state.currentPage = page
      if (onPageChange) {
        state.loading = true
        try {
          await onPageChange(page, state.pageSize)
        } finally {
          state.loading = false
        }
      }
    }
  }

  const changePageSize = async (newPageSize: number) => {
    if (newPageSize !== state.pageSize) {
      const oldPageSize = state.pageSize
      state.pageSize = newPageSize
      
      // Calculate new page to maintain roughly the same position
      const currentFirstItem = (state.currentPage - 1) * oldPageSize + 1
      const newPage = Math.ceil(currentFirstItem / newPageSize)
      state.currentPage = Math.max(1, newPage)

      if (onPageSizeChange) {
        state.loading = true
        try {
          await onPageSizeChange(newPageSize)
        } finally {
          state.loading = false
        }
      } else if (onPageChange) {
        state.loading = true
        try {
          await onPageChange(state.currentPage, newPageSize)
        } finally {
          state.loading = false
        }
      }
    }
  }

  const nextPage = async () => {
    if (hasNextPage.value) {
      await goToPage(state.currentPage + 1)
    }
  }

  const prevPage = async () => {
    if (hasPrevPage.value) {
      await goToPage(state.currentPage - 1)
    }
  }

  const reset = () => {
    state.currentPage = 1
    state.total = 0
  }

  const updateFromResponse = <T>(response: PaginatedResponse<T>) => {
    state.total = response.total
    // Update current page based on response
    state.currentPage = response.page
    state.pageSize = response.limit
  }

  const getQueryParams = (): PaginationQuery => {
    return {
      limit: state.pageSize,
      page: state.currentPage
    }
  }

  const getQueryParamsWithSearch = (searchQuery?: string): PaginationQuery => {
    const params = getQueryParams()
    if (searchQuery && searchQuery.trim()) {
      params.search = searchQuery.trim()
    }
    return params
  }

  return {
    // State
    state: readonly(state),
    
    // Computed
    totalPages,
    hasNextPage,
    hasPrevPage,
    startItem,
    endItem,
    
    // Methods
    goToPage,
    changePageSize,
    nextPage,
    prevPage,
    reset,
    updateFromResponse,
    getQueryParams,
    getQueryParamsWithSearch
  }
}

// Helper function to create pagination handlers
export function createPaginationHandlers<T>(
  loadDataFn: (params: PaginationQuery) => Promise<PaginatedResponse<T> | null>,
  onDataLoaded?: (data: T[]) => void
) {
  const handlePageChange = async (page: number, pageSize: number) => {
    const params: PaginationQuery = {
      limit: pageSize,
      page: page
    }
    
    const result = await loadDataFn(params)
    if (result && onDataLoaded) {
      onDataLoaded(result.data)
    }
  }

  const handlePageSizeChange = async (pageSize: number) => {
    // This will be handled by the pagination composable's changePageSize method
    // which will call onPageChange with the new page and page size
  }

  return {
    handlePageChange,
    handlePageSizeChange
  }
}
